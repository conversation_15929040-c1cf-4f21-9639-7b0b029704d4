const { environment } = require('@/config/environment.js')
import storage from '@/utils/storage'; // 假设你有封装好的本地存储工具
import { ACCESS_TOKEN } from '@/store/mutation-types' // 从 vuex 中导入 token

const baseUrl = environment.baseURL
/**
 * 上传图片到腾讯云 COS
 * @param {String} filePath - 要上传的本地图片路径
 * @param {Function} onSuccess - 成功回调，参数为返回的 url
 * @param {Function} onFail - 失败回调（可选）
 */
export function uploadImage(filePath, onSuccess, onFail) {
    uni.uploadFile({
        url: `${baseUrl}/common/tencent-cos/upload`,
        header: {
            'Authorization': 'Bearer ' + storage.get(ACCESS_TOKEN)
        },
        filePath: filePath,
        name: 'file',
        success: (res) => {
            try {
                const data = JSON.parse(res.data);
                if (data.code === 200) {
                    onSuccess && onSuccess(data.url, data.fileId);
                    uni.showToast({ title: '上传成功', icon: 'success' });
                } else {
                    uni.showToast({ title: '上传失败', icon: 'error' });
                    onFail && onFail(data);
                }
            } catch (err) {
                uni.showToast({ title: '上传异常', icon: 'error' });
                onFail && onFail(err);
            }
        },
        fail: (err) => {
            uni.showToast({ title: '上传失败', icon: 'error' });
            onFail && onFail(err);
        }
    });
}
