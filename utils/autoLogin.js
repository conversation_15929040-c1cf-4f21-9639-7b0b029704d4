/**
 * 自动登录工具
 * 封装微信自动登录逻辑，方便在多个页面中复用
 */

import { checkWechatRegistered } from "@/api/invited_register/invitedRegister"
import store from '@/store'

/**
 * 获取微信登录code
 * @returns {Promise} 返回微信登录结果
 */
function getWxLoginCode() {
  return new Promise((resolve, reject) => {
    // #ifdef MP-WEIXIN
    uni.login({
      provider: "weixin",
      success: resolve,
      fail: reject
    })
    // #endif
    // #ifndef MP-WEIXIN
    reject(new Error('非微信小程序环境'))
    // #endif
  })
}

/**
 * 执行自动登录
 * @param {Object} options 配置选项
 * @param {boolean} options.silent 是否静默模式（不输出日志）
 * @returns {Promise<Object>} 返回登录结果
 */
export async function autoLogin(options = {}) {
  const { silent = false } = options
  
  try {
    // 先获取用户信息，判断是否为游客
    await store.dispatch('Info')
    const userInfo = store.state.userInfo
    
    // 如果是游客，尝试自动登录
    if (userInfo?.guest !== false) {
      if (!silent) console.log('自动登录：检测到游客状态，尝试自动登录')

      const loginRes = await getWxLoginCode()
      const { data } = await checkWechatRegistered({ code: loginRes.code })

      if (data.isExists) {
        // 用户已注册，执行自动登录
        if (!silent) console.log('自动登录：用户已注册，执行自动登录')

        let loginRes2 = await getWxLoginCode()
        await store.dispatch('Login', {
          code: loginRes2.code,
          loginType: 'weixin',
          openid: data.openid
        })

        if (!silent) console.log('自动登录：自动登录成功')

        return {
          success: true,
          type: 'auto_login',
          message: '自动登录成功'
        }
      } else {
        if (!silent) console.log('自动登录：用户未注册，保持游客模式')

        return {
          success: true,
          type: 'guest',
          message: '用户未注册，保持游客模式'
        }
      }
    } else {
      if (!silent) console.log('自动登录：用户已登录')

      return {
        success: true,
        type: 'already_logged_in',
        message: '用户已登录'
      }
    }
  } catch (error) {
    if (!silent) console.warn('自动登录：过程中出现错误:', error)
    
    return {
      success: false,
      type: 'error',
      message: error.message || '自动登录失败',
      error
    }
  }
}

/**
 * 在页面中使用的自动登录方法
 * 这是一个便捷方法，会自动处理常见的使用场景
 * @param {Object} pageContext 页面上下文（this）
 * @param {Object} options 配置选项
 * @returns {Promise<Object>} 返回登录结果
 */
export async function autoLoginInPage(pageContext, options = {}) {
  const result = await autoLogin(options)
  
  // 如果页面有自定义的登录成功回调
  if (result.success && result.type === 'auto_login' && typeof pageContext.onAutoLoginSuccess === 'function') {
    try {
      await pageContext.onAutoLoginSuccess(result)
    } catch (error) {
      console.warn('自动登录成功回调执行失败:', error)
    }
  }

  // 如果页面有自定义的登录完成回调（无论成功失败都会调用）
  if (typeof pageContext.onAutoLoginComplete === 'function') {
    try {
      await pageContext.onAutoLoginComplete(result)
    } catch (error) {
      console.warn('自动登录完成回调执行失败:', error)
    }
  }
  
  return result
}

/**
 * 检查用户登录状态
 * @returns {Promise<Object>} 返回用户状态信息
 */
export async function checkLoginStatus() {
  try {
    await store.dispatch('Info')
    const userInfo = store.state.userInfo
    
    return {
      isLoggedIn: userInfo?.guest !== false,
      isGuest: userInfo?.guest !== false,
      userInfo: userInfo || null
    }
  } catch (error) {
    console.warn('检查登录状态失败:', error)
    return {
      isLoggedIn: false,
      isGuest: true,
      userInfo: null,
      error
    }
  }
}

export default {
  autoLogin,
  autoLoginInPage,
  checkLoginStatus
}
