import {checkWechatRegistered} from "@/api/invited_register/invitedRegister"

class LoginManager {
    constructor() {
        this.loginLoading = false
    }

    // 检查登录状态
    checkLoginStatus() {
        // 获取当前页面实例来访问 $store
        const pages = getCurrentPages()
        const currentPage = pages[pages.length - 1]

        if (currentPage && currentPage.$vm && currentPage.$vm.$store) {
            const userInfo = currentPage.$vm.$store.state.userInfo
            return userInfo && userInfo.userId
        }

        // 兜底方案：从本地存储获取
        const userInfo = uni.getStorageSync('userInfo')
        return userInfo && userInfo.guest !== true
    }

    // 显示登录确认弹窗
    showLoginModal(options = {}) {
        const {
            title = '登录提示',
            content = '此功能需要登录后才能使用，是否立即登录？',
            confirmText = '立即登录',
            cancelText = '取消',
            onConfirm = null,
            onCancel = null
        } = options

        return new Promise((resolve, reject) => {
            uni.showModal({
                title,
                content,
                confirmText,
                cancelText,
                success: (res) => {
                    if (res.confirm) {
                        this.performWxLogin()
                            .then(() => {
                                resolve(true)
                                onConfirm && onConfirm()
                            })
                            .catch((error) => {
                                reject(error)
                            })
                    } else {
                        resolve(false)
                        onCancel && onCancel()
                    }
                },
                fail: reject
            })
        })
    }

    // 执行微信登录
    async performWxLogin(inviterId = '') {
        if (this.loginLoading) {
            throw new Error('正在登录中...')
        }
        this.loginLoading = true
        try {
            // 如果没有传入inviterId，从localStorage获取
            if (!inviterId) {
                inviterId = uni.getStorageSync('inviterId') || ''
            }

            const loginRes = await this.getWxLoginCode()
            const {data} = await checkWechatRegistered({code: loginRes.code})

            if (data.isExists) {
                await this.executeLogin({
                    code: loginRes.code,
                    loginType: 'weixin',
                    openid: data.openid
                })
                this.showToast('登录成功', 'success')
                return true
            } else {
                const newLoginRes = await this.getWxLoginCode()
                await this.handleUnregisteredUser(newLoginRes.code, inviterId)
                return true
            }
        } catch (error) {
            console.error('微信登录失败:', error)
            this.showToast(error.message)
            throw error
        } finally {
            this.loginLoading = false
        }
    }

    // 处理未注册用户 - 使用弹窗注册
    async handleUnregisteredUser(code, inviterId = '') {
        return new Promise((resolve, reject) => {
            // 触发全局事件，让当前页面显示注册弹窗
            uni.$emit('showRegisterPopup', {
                code,
                inviterId,
                onSuccess: () => {
                    this.showToast('注册成功', 'success')
                    resolve(true)
                },
                onError: (error) => {
                    this.showToast('注册失败', 'error')
                    reject(error)
                },
                onCancel: () => {
                    // 用户取消注册，reject Promise以确保loginLoading被重置
                    reject(new Error('用户取消注册'))
                }
            })
        })
    }

    // 获取微信登录code
    getWxLoginCode() {
        return new Promise((resolve, reject) => {
            uni.login({
                provider: "weixin",
                success: resolve,
                fail: reject
            })
        })
    }

    // 执行登录操作
    async executeLogin(loginData) {
        // 获取当前页面实例来访问 $store
        const pages = getCurrentPages()
        const currentPage = pages[pages.length - 1]

        if (currentPage && currentPage.$vm && currentPage.$vm.$store) {
            await currentPage.$vm.$store.dispatch('Login', loginData)
            // 更新用户信息
            await currentPage.$vm.$store.dispatch('Info')
        } else {
            throw new Error('无法获取 store 实例')
        }
    }

    // 统一Toast提示
    showToast(title, icon = 'none') {
        uni.showToast({
            title,
            icon: icon === 'error' ? 'none' : icon,
            duration: 2000
        })
    }
}

// 创建单例
const loginManager = new LoginManager()

// 导出便捷方法
export const checkLogin = () => loginManager.checkLoginStatus()

export const requireLogin = (options = {}) => {
    if (loginManager.checkLoginStatus()) {
        return Promise.resolve(true)
    }
    return loginManager.showLoginModal(options)
}

export const performLogin = (inviterId) => loginManager.performWxLogin(inviterId)

export default loginManager