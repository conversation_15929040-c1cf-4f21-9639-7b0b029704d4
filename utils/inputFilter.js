/**
 * 输入过滤工具类
 * 用于处理各种输入格式的过滤和验证
 */

/**
 * 过滤非数字字符，只保留数字
 * @param {string} value - 输入值
 * @param {object} options - 配置选项
 * @param {boolean} options.allowDecimal - 是否允许小数点，默认false
 * @param {boolean} options.allowNegative - 是否允许负数，默认false
 * @param {number} options.maxLength - 最大长度限制
 * @param {number} options.decimalPlaces - 小数位数限制
 * @returns {string} 过滤后的值
 */
export function filterNumber(value, options = {}) {
  if (!value && value !== 0) return '';
  
  const {
    allowDecimal = false,
    allowNegative = false,
    maxLength = null,
    decimalPlaces = null
  } = options;
  
  let result = String(value);
  
  // 如果允许负数，保留开头的负号
  let hasNegative = false;
  if (allowNegative && result.startsWith('-')) {
    hasNegative = true;
    result = result.substring(1);
  }
  
  if (allowDecimal) {
    // 允许小数的情况
    // 只保留数字和第一个小数点
    result = result.replace(/[^\d.]/g, '');
    
    // 确保只有一个小数点
    const parts = result.split('.');
    if (parts.length > 2) {
      result = parts[0] + '.' + parts.slice(1).join('');
    }
    
    // 限制小数位数
    if (decimalPlaces !== null && parts.length === 2) {
      const decimalPart = parts[1].substring(0, decimalPlaces);
      result = parts[0] + (decimalPart ? '.' + decimalPart : '');
    }
  } else {
    // 只允许整数，移除所有非数字字符
    result = result.replace(/\D/g, '');
  }
  
  // 限制最大长度
  if (maxLength !== null && result.length > maxLength) {
    result = result.substring(0, maxLength);
  }
  
  // 添加负号
  if (hasNegative && result) {
    result = '-' + result;
  }
  
  return result;
}

/**
 * 过滤手机号，只保留数字，限制11位
 * @param {string} value - 输入值
 * @returns {string} 过滤后的手机号
 */
export function filterPhone(value) {
  return filterNumber(value, { maxLength: 11 });
}

/**
 * 过滤身份证号，只保留数字和X，限制18位
 * @param {string} value - 输入值
 * @returns {string} 过滤后的身份证号
 */
export function filterIdCard(value) {
  if (!value) return '';
  
  let result = String(value).toUpperCase();
  // 只保留数字和X
  result = result.replace(/[^\dX]/g, '');
  
  // 限制18位
  if (result.length > 18) {
    result = result.substring(0, 18);
  }
  
  return result;
}

/**
 * 过滤价格，保留两位小数
 * @param {string} value - 输入值
 * @returns {string} 过滤后的价格
 */
export function filterPrice(value) {
  return filterNumber(value, { 
    allowDecimal: true, 
    decimalPlaces: 2 
  });
}

/**
 * 过滤整数，只保留正整数
 * @param {string} value - 输入值
 * @param {number} maxLength - 最大长度
 * @returns {string} 过滤后的整数
 */
export function filterInteger(value, maxLength = null) {
  if (!value && value !== 0) return '';

  // 强制转换为字符串并移除所有非数字字符
  let result = String(value).replace(/\D/g, '');

  // 移除开头的0（除非整个值就是0）
  if (result.length > 1 && result.startsWith('0')) {
    result = result.replace(/^0+/, '') || '0';
  }

  // 限制最大长度
  if (maxLength !== null && result.length > maxLength) {
    result = result.substring(0, maxLength);
  }

  return result;
}

/**
 * 通用输入过滤器，根据输入类型自动选择过滤方法
 * @param {string} value - 输入值
 * @param {string} type - 输入类型：number, phone, idcard, price, integer
 * @param {object} options - 额外配置选项
 * @returns {string} 过滤后的值
 */
export function autoFilter(value, type, options = {}) {
  switch (type) {
    case 'number':
      return filterNumber(value, options);
    case 'phone':
      return filterPhone(value);
    case 'idcard':
      return filterIdCard(value);
    case 'price':
      return filterPrice(value);
    case 'integer':
      return filterInteger(value, options.maxLength);
    default:
      return value;
  }
}

/**
 * 创建实时输入过滤器，防止非法字符输入
 * @param {Function} filterFn - 过滤函数
 * @returns {Function} 返回一个可以绑定到input事件的函数
 */
export function createRealTimeFilter(filterFn) {
  return function(event) {
    const input = event.target || event.detail || {};
    const value = input.value || event;
    const filtered = filterFn(value);

    // 如果过滤后的值与原值不同，更新输入框
    if (value !== filtered) {
      if (input.value !== undefined) {
        input.value = filtered;
      }
      // 触发input事件，确保v-model更新
      if (typeof event === 'object' && event.target) {
        const inputEvent = new Event('input', { bubbles: true });
        event.target.dispatchEvent(inputEvent);
      }
    }

    return filtered;
  };
}

/**
 * 创建数字输入限制器
 * @param {number} maxLength - 最大长度
 * @returns {Function} 数字过滤函数
 */
export function createNumberFilter(maxLength = null) {
  return createRealTimeFilter((value) => filterInteger(value, maxLength));
}

/**
 * 创建输入过滤混入，可以在Vue组件中使用
 */
export const inputFilterMixin = {
  methods: {
    // 过滤数字输入
    filterNumberInput(value, options = {}) {
      return filterNumber(value, options);
    },
    
    // 过滤手机号输入
    filterPhoneInput(value) {
      return filterPhone(value);
    },
    
    // 过滤身份证输入
    filterIdCardInput(value) {
      return filterIdCard(value);
    },
    
    // 过滤价格输入
    filterPriceInput(value) {
      return filterPrice(value);
    },
    
    // 过滤整数输入
    filterIntegerInput(value, maxLength = null) {
      return filterInteger(value, maxLength);
    },
    
    // 通用过滤方法
    autoFilterInput(value, type, options = {}) {
      return autoFilter(value, type, options);
    }
  }
};
