/**
 * 日期工具函数
 * 主要解决iOS设备上日期格式兼容性问题
 */

/**
 * 将日期字符串转换为iOS兼容格式
 * iOS 只支持以下格式：
 * - "yyyy/MM/dd"
 * - "yyyy/MM/dd HH:mm:ss" 
 * - "yyyy-MM-dd"
 * - "yyyy-MM-ddTHH:mm:ss"
 * - "yyyy-MM-ddTHH:mm:ss+HH:mm"
 * 
 * @param {string} dateString - 原始日期字符串
 * @returns {string} - iOS兼容的日期字符串
 */
export function formatDateForIOS(dateString) {
  if (!dateString || typeof dateString !== 'string') {
    return dateString;
  }
  
  // 如果已经是ISO格式（包含T）或者斜杠格式，直接返回
  if (dateString.includes('T') || dateString.includes('/')) {
    return dateString;
  }
  
  // 将 "2025-07-18 00:00:00" 格式转换为 "2025/07/18 00:00:00"
  // 或者 "2025-07-18" 格式转换为 "2025/07/18"
  return dateString.replace(/-/g, '/');
}

/**
 * 创建一个iOS兼容的Date对象
 * @param {string|number|Date} dateInput - 日期输入
 * @returns {Date} - Date对象
 */
export function createSafeDate(dateInput) {
  if (!dateInput) {
    return new Date();
  }
  
  if (dateInput instanceof Date) {
    return dateInput;
  }
  
  if (typeof dateInput === 'number') {
    return new Date(dateInput);
  }
  
  if (typeof dateInput === 'string') {
    const formattedDate = formatDateForIOS(dateInput);
    return new Date(formattedDate);
  }
  
  return new Date(dateInput);
}

/**
 * 格式化日期为字符串
 * @param {Date|string|number} date - 日期
 * @param {string} format - 格式字符串，支持 YYYY-MM-DD HH:mm:ss
 * @returns {string} - 格式化后的日期字符串
 */
export function formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
  const d = createSafeDate(date);
  
  if (isNaN(d.getTime())) {
    console.error('Invalid date:', date);
    return '';
  }
  
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');
  const seconds = String(d.getSeconds()).padStart(2, '0');
  
  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds);
}
