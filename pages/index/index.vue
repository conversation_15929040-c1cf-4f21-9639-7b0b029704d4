<template>
  <view>
    <Navbar :hideBtn="true" bgColor="#f3f4f6"></Navbar>
    <view class="uni-margin-wrap">
      <swiper class="banner" :circular="true" :indicator-dots="true" :indicator-color="'rgba(0, 0, 0, .3)'" :indicator-active-color="'rgba(41,121,255,0.5)'" :autoplay="true" :interval="3000" :duration="500">
        <swiper-item v-for="(item, index) in bannerList" :key="index">
          <view class="banner-item" @click="onBannerClick(item)">
            <image class="banner-img" :src="item.img" mode="widthFix"/>
            <view v-if="item.title" class="banner-course-overlay">
              <view class="course-title">{{ item.title }}</view>
              <view class="course-meta" v-if="item.startTime">开始：{{ item.startTime }}</view>
              <view class="course-meta" v-if="item.endTime">结束：{{ item.endTime }}</view>
              <view class="course-meta" v-if="item.location">地址：{{ item.location }}</view>
            </view>
            <view class="banner-tip" v-if="item.title">点击报名</view>
          </view>
        </swiper-item>
      </swiper>
    </view>


    <!-- 功能 -->
    <view class="emergency-card">
      <u-grid :border="false" col="4">
        <u-grid-item @click="navigateTo('/pageA/course/list')" style="position:relative;">
          <u-icon name="list" color="#2979ff" size="60rpx"></u-icon>
          <text class="grid-text">最新课程</text>
          <view class="hot-badge-func">HOT</view>
        </u-grid-item>
        <u-grid-item @click="navigateTo('/pages/case/list')">
          <u-icon name="file-text-fill" color="#2979ff" size="60rpx"></u-icon>
          <text class="grid-text">案例展示</text>
        </u-grid-item>
        <u-grid-item @click="navigateTo('/pages/public/list')">
          <u-icon name="heart-fill" color="#2979ff" size="60rpx"></u-icon>
          <text class="grid-text">公益活动</text>
        </u-grid-item>
        <u-grid-item @click="navigateTo('/pages/calendar/index')">
          <u-icon name="calendar-fill" color="#2979ff" size="60rpx"></u-icon>
          <text class="grid-text">关于忻道</text>
        </u-grid-item>
      </u-grid>
    </view>


    <!-- 创始人介绍（卡片样式） -->
    <view class="team-section">
      <view class="founder-card" @click="navigateTo('/pageA/xin_intro/index')">
        <image class="founder-image" :src="founderImage" mode="widthFix"/>
        <view class="founder-text">
          <view class="name">忻自良</view>
          <view class="desc">中医生命急救三分钟</view>
          <view class="role">创始人</view>
        </view>
      </view>

      <view class="mentor-card" @click="navigateTo('/pageA/course/list')">
        <Calendar :courseDays="courseDays"/>
      </view>
    </view>

    <!-- 课程倒计时 -->
    <view class="countdown-card">
      <view class="countdown-title">距离最新课程开始还有</view>
      <view class="countdown-time">
        <text>{{ countdown.days }}天</text>
        <text>{{ countdown.hours }}小时</text>
        <text>{{ countdown.minutes }}分钟</text>
        <text>{{ countdown.seconds }}秒</text>
      </view>
    </view>

    <!-- 公益活动 -->
    <view class="public-section">
      <!-- 公益活动 + 更多 -->
      <view class="public-header">
        <text class="title">公益活动</text>
        <text class="more" @click="navigateTo('/pages/public/list')">更多 ></text>
      </view>

      <!-- 三张图片（动态） -->
      <view class="image-group" v-if="publicItem" @click="navigateTo('/pages/public/detail?id=' + publicItem.id)">
        <view
            class="image-wrapper"
            v-for="(img, idx) in publicItem.image"
            :key="idx"
        >
          <image
              :src="img"
              class="public-img"
              mode="aspectFill"
          />
        </view>
      </view>

      <!-- 标题 -->
      <view class="public-title">
        {{ publicItem.title }}
      </view>
    </view>


    <!-- 紧急求助 -->
    <view class="emergency-help">
      <u-button
          :type="emergencyBtnType"
          icon="phone-fill"
          :text="emergencyBtnText"
          @click="handleEmergency"
          :plain="true"
      />
    </view>


    <!-- 添加公众号关注组件 -->
    <!-- #ifdef MP-WEIXIN -->
    <official-account></official-account>
    <!-- #endif -->
  </view>
</template>

<script>
import Navbar from '@/components/navbar/Navbar'
import Calendar from '@/components/calendar/Calendar'
import {mapState} from 'vuex'
import {getCurrentMonthCourseDate, getLatestCourseTime} from '@/api/index/index'
import {createSafeDate} from '@/utils/dateUtils'
import { autoLoginInPage } from '@/utils/autoLogin'

export default {
  name: 'IndexPage',
  components: {
    Navbar,
    Calendar,
  },
  computed: {
    ...mapState(['userInfo']),
    isNotLoginOrGeneral() {
      if (!this.userInfo || this.userInfo.guest === true) return true;
      if (!this.userInfo.userType) return true;
      // 支持多类型逗号分隔
      return this.userInfo.userType.split(',').includes('general');
    },
    emergencyBtnText() {
      return this.isNotLoginOrGeneral ? '咨询电话' : '紧急求助';
    },
    emergencyPhone() {
      return this.isNotLoginOrGeneral ? '***********' : '**********';
    },
    emergencyBtnType() {
      return this.isNotLoginOrGeneral ? 'primary' : 'error';
    }
  },
  data() {
    return {
      founderImage: 'https://drxin-1359893946.cos.ap-shanghai.myqcloud.com/drxin-project/dev/Drxin_Founder_Introduction.png',
      courseDays: [],
      courseTime: '',
      countdown: {
        days: '00',
        hours: '00',
        minutes: '00',
        seconds: '00'
      },
      countdownTimer: null,
      publicItem: {
        id: 1,
        image: [
          // 'https://telegraph-image-92x.pages.dev/file/4f546bb623b5c5a73c597-304fcc624a36aa1a4e.jpg',
          // 'https://telegraph-image-92x.pages.dev/file/7334048b6ea14a8420af3-6ab6d4fd8225e094ce.jpg',
          // 'https://telegraph-image-92x.pages.dev/file/af476b91531eb5b7232c5-0f129822964a139a45.jpg'
        ],
        title: '生命急救，爱在明珠——忻医生中医生生命急救三分钟雄安公益讲座圆满成功'
      },
      bannerList: [
        {
          img: '/static/asset/latest_course.png',
        }
      ]
    }
  },
  mounted() {
    // 自动登录逻辑：仅访客时
    autoLoginInPage(this)

    getLatestCourseTime().then(response => {
      if (response.data && response.data.length > 0) {
        const courseBanners = response.data.map(course => ({
            img: '/static/asset/latest_course.png',
            title: course.courseName,
            startTime: course.startTime,
            endTime: course.endTime,
            location: course.location,
            url: '/pageA/course/detail?id=' + course.id
          }))
          // 将课程banner插入到bannerList最前面
        this.bannerList = [...courseBanners]
        this.courseTime = response.data[0].startTime;

        // 获取最新课程时间成功后，开始倒计时
        this.startCountdown();
      } else {
        console.warn("没有获取到课程数据");
        // 设置默认倒计时显示
        this.countdown = {days: '00', hours: '00', minutes: '00', seconds: '00'};
      }
    }).catch(error => {
      console.error("获取最新课程时间失败：", error);
      // 设置默认倒计时显示
      this.countdown = {days: '00', hours: '00', minutes: '00', seconds: '00'};
    });

    getCurrentMonthCourseDate().then(response => {
      this.courseDays = response.data.map(date => {
        return Number(date); // 只取日期部分
      });
    }).catch(error => {
      console.error("获取当前月份课程日期失败：", error);
    });
  },
  methods: {
    navigateTo(url) {
      // console.log("url===========", url)
      // 提示暂未开发
      uni.navigateTo({
        url: url, fail: () => {
          uni.showToast({
            title: '功能开发中，敬请期待！',
            icon: 'none'
          })
        }
      })
    },
    handleEmergency() {
      // 处理紧急求助
      uni.showModal({
        title: this.emergencyBtnText,
        content: `是否拨打${this.emergencyBtnText}？`,
        success: (res) => {
          if (res.confirm) {
            uni.makePhoneCall({
              phoneNumber: this.emergencyPhone
            })
          }
        }
      })
    },
    startCountdown() {
      // 检查课程时间是否有效
      if (!this.courseTime) {
        console.warn("课程时间为空，无法开始倒计时");
        this.countdown = {days: '00', hours: '00', minutes: '00', seconds: '00'};
        return;
      }

      // 使用安全的日期创建方法，自动处理iOS兼容性
      const targetDate = createSafeDate(this.courseTime);

      // 检查日期是否有效
      if (isNaN(targetDate.getTime())) {
        console.error("无效的课程时间格式：", this.courseTime);
        this.countdown = {days: '00', hours: '00', minutes: '00', seconds: '00'};
        return;
      }

      const target = targetDate.getTime();

      // 清除之前的定时器
      if (this.countdownTimer) {
        clearInterval(this.countdownTimer);
      }

      this.countdownTimer = setInterval(() => {
        const now = new Date().getTime()
        const diff = target - now

        if (diff <= 0) {
          clearInterval(this.countdownTimer)
          this.countdown = {days: '00', hours: '00', minutes: '00', seconds: '00'}
          return
        }

        const days = Math.floor(diff / (1000 * 60 * 60 * 24))
        const hours = Math.floor((diff / (1000 * 60 * 60)) % 24)
        const minutes = Math.floor((diff / (1000 * 60)) % 60)
        const seconds = Math.floor((diff / 1000) % 60)

        this.countdown = {
          days: days.toString().padStart(2, '0'),
          hours: hours.toString().padStart(2, '0'),
          minutes: minutes.toString().padStart(2, '0'),
          seconds: seconds.toString().padStart(2, '0')
        }
      }, 1000)
    },
    onBannerClick(item) {
      if (item && item.url) {
        this.navigateTo(item.url)
      }
    },

    // 自动登录成功回调（可选）
    onAutoLoginSuccess(result) {
      console.log('首页：自动登录成功回调', result)
      // 这里可以添加登录成功后的特殊处理逻辑
      // 比如刷新某些数据、显示欢迎信息等
    },

    // 自动登录完成回调（可选）
    onAutoLoginComplete(result) {
      console.log('首页：自动登录完成', result)
      // 这里可以添加登录完成后的处理逻辑
      // 无论成功失败都会执行
    }
  },
  // 微信小程序分享功能
  onShareAppMessage(res) {
    const currentUser = this.$store.state.userInfo
    const inviterId = currentUser?.userId || ''

    return {
      title: '忻道',
      path: `/pages/index/index?inviterId=${inviterId}`,
      imageUrl: '/static/asset/share_logo.png'
    }
  },
  // 分享到朋友圈
  onShareTimeline() {
    const currentUser = this.$store.state.userInfo
    const inviterId = currentUser?.userId || ''

    return {
      title: '忻道',
      query: `inviterId=${inviterId}`,
      imageUrl: '/static/asset/share_logo.png'
    }
  },
  beforeDestroy() {
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer)
    }
  }
}
</script>

<style lang="scss">
.banner {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 32rpx 0 0 0;
  position: relative;
}

.banner-img {
  width: 90vw;
  max-width: 700rpx;
  margin-left: 40rpx;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.10);
  display: block;
}

.banner-tip {
  position: absolute;
  left: 50%;
  bottom: 110rpx;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.45);
  color: #fff;
  font-size: 32rpx;
  padding: 8rpx 32rpx;
  border-radius: 32rpx;
  z-index: 2;
  letter-spacing: 2rpx;
}

.grid-text {
  font-size: 26rpx;
  color: #333;
}

.emergency-card {
  background-color: #fff;
  padding: 30rpx;
  margin: 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);

  .card-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30rpx;

    .title-text {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }

    .more-text {
      font-size: 26rpx;
      color: #666;
    }
  }

  .card-content {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;

    .knowledge-item {
      width: 25%;
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 20rpx;

      .item-icon {
        width: 80rpx;
        height: 80rpx;
        margin-bottom: 10rpx;
      }

      .item-text {
        font-size: 24rpx;
        color: #666;
      }
    }
  }
}

.training-section {
  background-color: #fff;
  padding: 30rpx;
  margin: 20rpx;

  .section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 30rpx;
  }

  .training-list {
    .training-item {
      display: flex;
      margin-bottom: 30rpx;
      padding: 20rpx;
      background-color: #f8f8f8;
      border-radius: 12rpx;

      .training-image {
        width: 200rpx;
        height: 150rpx;
        border-radius: 8rpx;
        margin-right: 20rpx;
      }

      .training-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .training-title {
          font-size: 28rpx;
          font-weight: bold;
          color: #333;
        }

        .training-desc {
          font-size: 24rpx;
          color: #666;
          margin: 10rpx 0;
        }

        .training-meta {
          display: flex;
          justify-content: space-between;
          font-size: 22rpx;
          color: #999;

          .training-time, .training-location {
            display: flex;
            align-items: center;
          }
        }
      }
    }
  }
}


.team-section {
  display: flex;
  width: 95%;
  gap: 20rpx;
}

.founder-card,
.mentor-card {
  width: 50%;
  left: 20rpx;
  position: relative;
  border-radius: 24rpx;
  overflow: hidden;
}

.founder-image,
.mentor-image {
  width: 100%;
  display: block;
}

.founder-text,
.mentor-text {
  position: absolute;
  left: 32rpx;
  top: 32rpx;
  color: #ffffff;
  z-index: 10;
}

.founder-text .name,
.mentor-text .name {
  font-size: 36rpx;
  font-weight: 700;
}

.founder-text .desc,
.mentor-text .desc {
  font-size: 24rpx;
  margin-top: 6rpx;
}

.founder-text .role {
  font-size: 24rpx;
  margin-top: 4rpx;
}

.countdown-card {
  background-color: #fffbe6;
  border-left: 8rpx solid #f6c44f;
  margin: 20rpx;
  padding: 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);

  .countdown-title {
    font-size: 28rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 16rpx;
  }

  .countdown-time {
    display: flex;
    gap: 20rpx;
    font-size: 32rpx;
    font-weight: bold;
    color: #f56c6c;
  }
}

.public-section {
  padding: 30rpx;
  background-color: #fff;
}

.public-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;

  .title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
  }

  .more {
    font-size: 28rpx;
    color: #888;
  }
}

.image-group {
  display: flex;
  justify-content: space-between;
  gap: 16rpx;

  .image-wrapper {
    flex: 1;

    .public-img {
      width: 100%;
      height: 180rpx;
      border-radius: 12rpx;
      object-fit: cover;
    }
  }
}

.public-title {
  margin-top: 24rpx;
  font-size: 30rpx;
  color: #222;
  font-weight: 500;
  line-height: 1.4;
}

.emergency-help {
  position: fixed;
  bottom: 80rpx;
  right: 40rpx;
  z-index: 999;
  pointer-events: auto;
}

.banner-course-overlay {
  position: absolute;
  left: 50rpx;
  bottom: 0;
  min-width: 340rpx;
  background: none;
  border-radius: 0;
  // padding: 8rpx 18rpx 8rpx 18rpx;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  box-shadow: none;

  .course-title {
    font-weight: bold;
    color: #2979ff;
    font-size: 22rpx;
    margin-bottom: 4rpx;
    text-align: left;
    width: 100%;
  }

  .course-meta {
    color: #333;
    font-size: 18rpx;
    margin-bottom: 1rpx;
    text-align: left;
    width: 100%;
  }
}

.hot-badge-func {
  position: absolute;
  top: 0;
  right: 18rpx;
  background: #ff3b30;
  color: #fff;
  font-size: 18rpx;
  font-weight: bold;
  border-radius: 18rpx;
  padding: 2rpx 10rpx;
  z-index: 10;
  letter-spacing: 1rpx;
  box-shadow: 0 2rpx 8rpx rgba(255,59,48,0.15);
}

</style>
