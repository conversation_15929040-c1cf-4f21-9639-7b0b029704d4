<template>
  <view class="page-container">
    <Navbar title="我的课程" bgColor="#f3f4f6" :hideBtn="true" :fixed="false" />

    <!-- 标签页 -->
    <view class="tabs-container">
      <u-tabs v-if="tabs.length" :list="tabs" :current="activeTab" @click="tabChange" />
    </view>

    <!-- 内容区域 -->
    <view class="content-container">
      <!-- 未登录状态 -->
      <view v-if="!isLoggedIn" class="login-required-container">
        <view class="login-prompt">
          <view class="prompt-icon">🔐</view>
          <view class="prompt-title">请先登录</view>
          <view class="prompt-desc">登录后即可查看您的课程和活动</view>
          <view class="login-benefits">
            <view class="benefit-item">
              <text class="benefit-icon">📚</text>
              <text class="benefit-text">查看已报名的课程</text>
            </view>
          </view>
          <button class="login-btn" @click="handleNeedLogin">立即登录</button>
        </view>
      </view>

      <!-- 已登录状态 -->
      <template v-else>
        <!-- 课程列表 -->
        <view v-if="activeTab === 0">
          <DataList
              ref="courseDataList"
              :labels="courseLabels"
              :data="courses"
              :loading="courseLoading"
              :pagination="coursePagination"
              @refresh="onCourseRefresh"
              @load-more="onCourseLoadMore"
              @item-click="onCourseClick"
          />
        </view>

        <!-- 活动列表 -->
        <view v-else>
          <DataList
              ref="eventDataList"
              :labels="eventLabels"
              :data="events"
              :loading="eventLoading"
              :pagination="eventPagination"
              @refresh="onEventRefresh"
              @load-more="onEventLoadMore"
              @item-click="onEventClick"
          />
        </view>
      </template>
    </view>
    <!-- 添加注册弹窗组件 -->
    <RegisterPopup
        :show="showRegisterPopup"
        @close="closeRegisterPopup"
        @submit="handleRegisterSubmit"
    />
  </view>
</template>

<script>
import Navbar from '@/components/navbar/Navbar'
import DataList from '@/components/data-list/DataList'
import RegisterPopup from "@/components/register-popup/RegisterPopup.vue";
import { loginMixin } from '@/mixins/loginMixin'
import { getMyCourseList } from '@/api/work/course'

export default {
  components: {RegisterPopup, Navbar, DataList },
  mixins: [loginMixin],
  data() {
    return {
      activeTab: 0,
      tabs: [
        { name: '课程', key: 'course' }
      ],

      // 课程相关数据
      courseLabels: [
        { label: '课程名称', prop: 'courseName' },
        { label: '开始时间', prop: 'startTime' },
        { label: '结束时间', prop: 'endTime' },
        { label: '上课地点', prop: 'location' }
      ],
      courses: [],
      courseLoading: false,
      coursePagination: {
        page: 1,
        pageSize: 10,
        total: 0,
      },

      // 活动相关数据
      eventLabels: [
        { label: '活动名称', prop: 'eventName' },
        { label: '活动时间', prop: 'eventTime' },
        { label: '活动地点', prop: 'location' },
        { label: '主办方', prop: 'organizer' },
        { label: '参与情况', prop: 'participation' },
        { label: '状态', prop: 'statusLabel' }
      ],
      events: [],
      eventLoading: false,
      eventPagination: {
        page: 1,
        pageSize: 10,
        total: 0,
      },

      // 登录状态
      isLoggedIn: false
    }
  },

  onShow() {
    // 检查登录状态
    this.isLoggedIn = this.$store.state.userInfo && this.$store.state.userInfo.userId;
    this.loadInitialData();
  },

  methods: {
    tabChange(index) {
      this.activeTab = index.index;

      // 未登录状态下不加载数据
      if (!this.isLoggedIn) return;

      // 切换标签页时加载对应数据
      if (this.activeTab === 0 && this.courses.length === 0) {
        this.loadCourses();
      } else if (this.activeTab === 1 && this.events.length === 0) {
        this.loadEvents();
      }
    },

    loadInitialData() {
      // 默认加载课程数据
      // 判断标签页
      if (this.activeTab === 0 && this.courses.length === 0) {
        this.loadCourses();
      } else if (this.activeTab === 1 && this.events.length === 0) {
        this.loadEvents();
      }
    },
    // 课程相关方法
    async loadCourses(append = false) {
      if (!this.isLoggedIn) return;

      this.courseLoading = true;
      try {
        // 这里应该调用实际的API
        const res = await getMyCourseList({
          pageNum: this.coursePagination.page,
          pageSize: this.coursePagination.pageSize,
          userId: this.$store.state.userInfo.userId
        });

        // 处理状态样式
        const processedData = res.rows.map(item => ({
          ...item,
        }));

        if (append) {
          this.courses = [...this.courses, ...processedData];
        } else {
          this.courses = processedData;
        }

        this.coursePagination.total = res.total;

      } catch (error) {
        console.error('获取课程列表失败:', error);
        uni.showToast({ title: '加载失败', icon: 'none' });
      } finally {
        this.courseLoading = false;
        // 停止课程 DataList 组件的刷新状态
        if (this.$refs.courseDataList) {
          this.$refs.courseDataList.stopRefresh();
        }
      }
    },

    // 活动相关方法
    async loadEvents(append = false) {
      if (!this.isLoggedIn) return;

      this.eventLoading = true;
      try {
        // 这里应该调用实际的API
        // const res = await getMyEventList({
        //   pageNum: this.eventPagination.page,
        //   pageSize: this.eventPagination.pageSize
        // });

        // 模拟API响应数据
        const mockData = {
          rows: [
            {
              id: 1,
              eventName: '技术分享会：AI时代的前端开发',
              eventTime: '2024-12-25 14:00-16:00',
              location: '学术报告厅',
              organizer: '计算机学院',
              participation: '已报名 125/150人',
              status: 'upcoming',
              statusLabel: '即将开始'
            },
            {
              id: 2,
              eventName: '编程马拉松比赛',
              eventTime: '2024-12-20 09:00-18:00',
              location: '创新实验室',
              organizer: '学生会技术部',
              participation: '已参加 48/50人',
              status: 'ended',
              statusLabel: '已结束'
            }
          ],
          total: 2
        };

        // 处理状态样式
        const processedData = mockData.rows.map(item => ({
          ...item,
        }));

        if (append) {
          this.events = [...this.events, ...processedData];
        } else {
          this.events = processedData;
        }

        this.eventPagination.total = mockData.total;

      } catch (error) {
        console.error('获取活动列表失败:', error);
        uni.showToast({ title: '加载失败', icon: 'none' });
      } finally {
        this.eventLoading = false;
        // 停止活动 DataList 组件的刷新状态
        if (this.$refs.eventDataList) {
          this.$refs.eventDataList.stopRefresh();
        }
      }
    },
    // 登录相关方法
    async handleNeedLogin() {
      await this.requireLogin(async () => {
        this.isLoggedIn = true;
        this.loadInitialData();
      }, {
        content: '此操作需要登录，是否立即登录？'
      })
    },
    // 课程事件处理
    onCourseRefresh() {
      this.coursePagination.page = 1;
      this.loadCourses(false);
    },

    onCourseLoadMore() {
      if (this.courses.length < this.coursePagination.total) {
        this.coursePagination.page++;
        this.loadCourses(true);
      }
    },

    onCourseClick(course) {
      // 跳转到课程详情页
      let id = course.courseId
      uni.navigateTo({
        url: `/pageA/course/detail?id=${id}`
      });
    },

    // 活动事件处理
    onEventRefresh() {
      this.eventPagination.page = 1;
      this.loadEvents(false);
    },

    onEventLoadMore() {
      if (this.events.length < this.eventPagination.total) {
        this.eventPagination.page++;
        this.loadEvents(true);
      }
    },

    onEventClick(event) {
      uni.navigateTo({
        url: `/pageA/event/my-detail?id=${event.id}`
      });
    }
  },
  // 微信小程序分享功能
  onShareAppMessage(res) {
    return {
      title: '忻道-我的课程',
      path: '/pages/work/index',
      imageUrl: 'https://drxin-1359893946.cos.ap-shanghai.myqcloud.com/drxin-project/dev/share_logo.png'
    }
  },
  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '忻道-我的课程',
      query: '',
      imageUrl: 'https://drxin-1359893946.cos.ap-shanghai.myqcloud.com/drxin-project/dev/share_logo.png'
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
}

.tabs-container {
  background-color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.03);
}

.content-container {
  flex: 1;
  padding: 20rpx 30rpx;
}

/* 未登录状态样式 */
.login-required-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 40rpx;
}

.login-prompt {
  text-align: center;
  background: #fff;
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.06);
  max-width: 600rpx;
  width: 100%;
}

.prompt-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.8;
}

.prompt-title {
  font-size: 40rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.prompt-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

.login-benefits {
  margin-bottom: 48rpx;
}

.benefit-item {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 20rpx;
  padding: 0 20rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.benefit-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.benefit-text {
  font-size: 28rpx;
  color: #555;
}

.login-btn {
  background: linear-gradient(135deg, #2f80ed 0%, #1e6bb8 100%);
  color: #fff;
  border: none;
  border-radius: 50rpx;
  padding: 24rpx 60rpx;
  font-size: 32rpx;
  font-weight: 500;
  box-shadow: 0 8rpx 20rpx rgba(47, 128, 237, 0.3);
  transition: all 0.3s ease;

  &:active {
    transform: translateY(2rpx);
    box-shadow: 0 4rpx 12rpx rgba(47, 128, 237, 0.4);
  }
}

/* 自定义列表项样式 - 与list.vue保持一致 */
:deep(.apply-list-item) {
  border-radius: 16rpx !important;
  box-shadow: 0 6rpx 18rpx rgba(0, 0, 0, 0.05) !important;
  margin-bottom: 24rpx !important;
  border-left: 8rpx solid #2f80ed;
}

:deep(.apply-list-row) {
  margin-bottom: 12rpx !important;
}

:deep(.apply-list-label) {
  color: #666 !important;
  font-weight: 500 !important;
  min-width: 140rpx !important;
}

/* 状态标签样式 */
:deep(.status-tag) {
  display: inline-block;
  padding: 4rpx 16rpx;
  border-radius: 24rpx;
  font-size: 24rpx;
  font-weight: 500;

  &.status-ongoing {
    background-color: #e6f7ff;
    color: #1890ff;
  }

  &.status-completed {
    background-color: #f6ffed;
    color: #52c41a;
  }

  &.status-paused {
    background-color: #fff7e6;
    color: #fa8c16;
  }

  &.status-upcoming {
    background-color: #f9f0ff;
    color: #722ed1;
  }

  &.status-ended {
    background-color: #f5f5f5;
    color: #8c8c8c;
  }
}

/* 活动标签页特殊样式 */
.content-container:has([data-tab="event"]) {
  :deep(.apply-list-item) {
    border-left-color: #722ed1;
  }
}

/* 响应式优化 */
@media (max-width: 750rpx) {
  :deep(.apply-list-label) {
    min-width: 120rpx !important;
    font-size: 26rpx !important;
  }
}
</style>