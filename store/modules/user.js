import {ACCESS_TOKEN, REFRESH_TOKEN} from '@/store/mutation-types'
import storage from '@/utils/storage'
import * as LoginApi from '@/api/login'
import * as UserApi from '@/api/user'

// 登陆成功后执行
const loginSuccess = (commit, {accessToken, refreshToken}) => {
    // 过期时间30天
    const refreshExpiryTime = 30 * 86400
    // 保存token和userId到缓存
    storage.set(ACCESS_TOKEN, accessToken, refreshExpiryTime)
    storage.set(REFRESH_TOKEN, refreshToken, refreshExpiryTime)
    // 记录到store全局变量
    commit('SET_TOKEN', accessToken)
    commit('SET_GUEST', false)
    commit('SET_USER', null)
}

export const state = {
    // 用户认证token
    token: '',
    // 用户信息
    userInfo: null,
    // 是否为游客模式
    isGuest: true
}

export const mutations = {
    SET_TOKEN: (state, value) => {
        state.token = value
    },
    SET_USER: (state, value) => {
        state.userInfo = value
    },
    SET_GUEST: (state, value) => {
        state.isGuest = value
    },
    UPDATE_USER_CONTRIBUTION: (state, value) => {
        if (state.userInfo) {
            state.userInfo.contribution = value
        }
    }
}

export const actions = {
    // 用户登录(支持普通登录和微信登录)
    Login({commit}, data) {
        return new Promise((resolve, reject) => {
            // 根据登录类型选择不同的登录接口
            const loginMethod = data.loginType === 'weixin' ?
                LoginApi.wxLogin :
                LoginApi.login

            loginMethod(data, {custom: {catch: true}}).then(response => {
                loginSuccess(commit, response.data)
                resolve(response)
            }).catch(reject)
        })
    },

    // 用户信息
    Info({commit, state}) {
        return new Promise((resolve, reject) => {
            if (state.userInfo && state.userInfo.realName && !state.isGuest) {
                return resolve(state.userInfo)
            }
            // 如果是游客模式，返回游客信息
            if (state.isGuest) {
                const guestInfo = {
                    user: {
                        nickName: '游客',
                        userName: 'guest',
                        email: '',
                        isGuest: true,
                        phonenumber: '',
                        dept: {deptName: '未登录'}
                    }
                }
                commit('SET_USER', guestInfo.user)
                return resolve(guestInfo)
            }
            // 否则获取真实用户信息
            UserApi.getInfo().then(response => {
                commit('SET_USER', response.user)
                resolve(response)
            }).catch(reject)
        })
    },

    // 获取用户贡献值
    Contribution({commit}, data) {
        return new Promise((resolve, reject) => {
            UserApi.getContribution().then(response => {
                commit('UPDATE_USER_CONTRIBUTION', response.data)
                resolve(response)
            }).catch(reject)
        })
    },

    // 退出登录
    Logout({commit}, data) {
        return new Promise((resolve, reject) => {
            LoginApi.logout(data, {custom: {catch: true}}).then(response => {
                storage.remove(ACCESS_TOKEN)
                commit('SET_TOKEN', '')
                commit('SET_GUEST', true)
                commit('SET_USER', {
                    nickName: '游客',
                    userName: 'guest',
                    email: '',
                    phonenumber: '',
                    avatar: '',
                    guest: true,
                    roles: [],
                    dept: {deptName: '未登录'}
                })
                resolve(response)
            }).catch(reject)
        })
    }
}
