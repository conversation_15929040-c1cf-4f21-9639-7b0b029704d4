<template>
  <view>
    <Navbar :hideBtn="false" bgColor="#f3f4f6" title="邀请注册"></Navbar>
    <view class="container">
      <button
          class="wechat-login-btn"
          open-type="getUserInfo"
          @tap="onWeChatLogin"
      >
        微信授权注册
      </button>
    </view>
    <RegisterPopup
      :show="showRegisterPopup"
      @close="closeRegisterPopup"
      @submit="handleRegisterSubmit"
    />
  </view>
</template>

<script>
import Navbar from '@/components/navbar/Navbar'
import { loginMixin } from '@/mixins/loginMixin'
import loginManager from '@/utils/loginUtils'
import RegisterPopup from '@/components/register-popup/RegisterPopup'

export default {
  components: {
    Navbar,
    RegisterPopup
  },
  mixins: [loginMixin],
  data() {
    return {
      scene: null,
      inviterId: null,
    }
  },
  async onLoad(options) {
    if (options.scene) {
      this.scene = decodeURIComponent(options.scene)
      this.inviterId = this.scene.split('=')[1]
    }
    try {
      await loginManager.performWxLogin(this.inviterId)
      uni.switchTab({
        url: '/pages/center/index'
      })
    } catch (e) {
      // 登录失败或用户需要注册时，注册弹窗会自动弹出
    }
  },
  methods: {
    onWeChatLogin() {
      console.log('onWeChatLogin')
      loginManager.performWxLogin(this.inviterId)
    },
    // 其余注册弹窗相关方法由loginMixin提供
  }
}
</script>

<style>
.container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80vh;
}

.wechat-login-btn {
  width: 80vw;
  height: 44px;
  background: #07c160;
  color: #fff;
  font-size: 18px;
  border-radius: 6px;
}

.popup-form {
  padding: 24rpx;
}

.form-tip {
  color: #faad14;
  font-size: 30rpx;
  text-align: center;
  margin-bottom: 24rpx;
  font-weight: bold;
}
</style>
