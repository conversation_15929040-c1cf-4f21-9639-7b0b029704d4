<template>
  <view class="query-info-container">
    <navbar title="信息查询" />

    <view v-if="!searched" class="query-form-wrapper">
      <view class="query-form">
        <u--form :model="form" ref="uForm" :label-width="0">
          <u-form-item prop="idCard">
            <u--input v-model="form.idCard" placeholder="请输入证件号码" clearable border="none" />
          </u-form-item>
        </u--form>
      </view>
      <u-button @click="handleQuery" type="primary" :disabled="loading">
        <text v-if="!loading">查询</text>
        <u-loading-icon v-else mode="circle" color="#ffffff"></u-loading-icon>
      </u-button>
    </view>

    <view v-else class="query-result">
      <view class="result-card">
        <view class="result-item">
          <text class="label">姓名</text>
          <text class="value">{{ result.name }}</text>
        </view>
        <view class="result-item">
          <text class="label">性别</text>
          <text class="value">{{ result.gender }}</text>
        </view>
        <view class="result-item">
          <text class="label">证件号</text>
          <text class="value">{{ result.idCard }}</text>
        </view>
        <view class="result-item">
          <text class="label">角色名称</text>
          <text class="value">{{ result.roleName }}</text>
        </view>
      </view>
      <view class="button-group">
        <u-button @click="reset" type="info" :plain="true">重新查询</u-button>
      </view>
    </view>

    <!-- 返回主页 悬浮按钮 -->
    <view class="floating-button">
      <u-button
        type="primary"
        shape="circle"
        :custom-style="returnButtonStyle"
        @click="goHome"
      >
        <u-icon name="home" color="#fff" size="20"></u-icon>
      </u-button>
    </view>
  </view>
</template>

<script>
import Navbar from '@/components/navbar/Navbar.vue'

export default {
  components: {
    Navbar
  },
  data() {
    return {
      form: {
        idCard: ''
      },
      searched: false,
      loading: false,
      result: {
        name: '',
        gender: '',
        idCard: '',
        roleName: ''
      }
    }
  },
  computed: {
    // 返回按钮样式
    returnButtonStyle() {
      return {
        width: '50px',
        height: '50px',
        background: 'linear-gradient(135deg, #2F80ED 0%, #56CCF2 100%)',
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
      }
    }
  },
  methods: {
    handleQuery() {
      if (!this.form.idCard) {
        uni.showToast({
          title: '请输入证件号码',
          icon: 'none'
        });
        return;
      }
      this.loading = true;

      // 模拟API调用
      setTimeout(() => {
        // 在实际应用中，您应该在此处调用API来获取数据
        // const res = await yourApi.getInfoById(this.form.idCard);
        this.result = {
          name: '张三',
          gender: '男',
          idCard: this.form.idCard,
          roleName: '应急救援员'
        };
        this.searched = true;
        this.loading = false;
      }, 1000);
    },
    goHome() {
      uni.switchTab({
        url: '/pages/index/index'
      });
    },
    reset() {
      this.form.idCard = '';
      this.searched = false;
      this.result = {
        name: '',
        gender: '',
        idCard: '',
        roleName: ''
      };
    }
  }
}
</script>

<style lang="scss" scoped>
.query-info-container {
  min-height: 100vh;
  background-color: #f4f6f8;
}

.query-form-wrapper {
  padding: 80rpx 40rpx;
}

.query-form {
  background-color: #ffffff;
  padding: 10rpx 30rpx;
  border-radius: 16rpx;
  margin-bottom: 40rpx;
  border: 1rpx solid #e0e4e9;
}

.query-result {
  padding: 40rpx;
}

.result-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.05);
}

.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }

  .label {
    color: #606266;
    font-size: 28rpx;
  }

  .value {
    color: #303133;
    font-size: 28rpx;
    font-weight: 500;
  }
}

.button-group {
  display: flex;
  gap: 20rpx;
}

.floating-button {
  position: fixed;
  right: 20px;
  bottom: 100px;
  z-index: 10;
}
</style>
