<template>
    <view class="page-container">
      <Navbar bgColor="#f3f4f6" :title="title"></Navbar>
      <view class="title">忻自良：中医急救领域的传奇人物</view>
       <!-- 视频区域 -->
        <view class="video-wrapper">
        <video
            id="introVideo"
            :src="videoUrl"
            controls
            :autoplay="false"
            :loop="false"
            referrer-policy="origin"
            :muted="false"
            objectFit="contain"
            style="width: 100%; height: 380rpx; border-radius: 16rpx;"
        />
        </view>

      <view class="section">
        <view class="section-title">人物简介</view>
        <text class="paragraph">
          忻自良，著名中医急救专家，拥有深厚中医世家背景，师承联合国非遗针灸代表性传承人张缙教授，致力于中医与现代医学融合创新。曾旅居加拿大多年，为多位政要、社会名流提供中医诊疗服务，在国际中医界享有盛誉。
        </text>
      </view>
  
      <view class="section">
        <view class="section-title">创新成就</view>
        <text class="paragraph">
          2018年，忻自良创立《中医生命急救三分钟》项目，提出“让每个家庭都有一名生命急救员”的宏愿，研发出一整套适用于院前急救的中医技术体系，极大填补了传统急救空白，推动了中医在现代生命急救中的实际应用。
        </text>
      </view>
  
      <view class="section">
        <view class="section-title">核心技术</view>
        <text class="paragraph">
          项目以“安全、简易、有效”为宗旨，开发了如“中医醒神开窍术”、“中医心包经泵压术”、“五心运气回阳术”等急救技法，帮助普通家庭掌握黄金三分钟内的自救与他救方法，广泛应用于脑卒中、心梗等重症场景。
        </text>
      </view>
  
      <view class="section">
        <view class="section-title">推广模式</view>
        <text class="paragraph">
          通过线上空中课堂与线下实操培训双线并举，使中医急救知识真正走入社区与家庭。目前，该项目已在多个国家和地区落地，影响力持续扩大。
        </text>
      </view>
  
      <view class="section">
        <view class="section-title">“三 XIN”理念</view>
        <text class="paragraph">
          忻自良秉持“创新、用心、互信”的“三 XIN”理念：持续技术创新、真诚服务患者、构建医患互信，推动中医急救在全球健康领域的可持续发展。
        </text>
      </view>
  
      <view class="section">
        <view class="section-title">人物影响</view>
        <text class="paragraph">
          他不仅推动中医急救进入现代公共急救体系，还以身作则传播中医文化，成为中华传统医学在国际舞台上的杰出代表与传承典范。
        </text>
      </view>
    </view>
  </template>
  <script>
 import Navbar from '@/components/navbar/Navbar.vue'
  export default {
    components: {
      Navbar
    },
    data() {
      return {
        title: '创始人介绍',
        videoUrl: 'https://wexinapp.drxin.com.cn/drxin-project/dev/video/xin-intro/xin_intro.mp4'
        // videoUrl: 'https://drxin-1359893946.cos.ap-shanghai.myqcloud.com/drxin-project/dev/video/xin-intro/xin_intro.mp4'
      }
    },
    onLoad(options) {
      // 缓存邀请人ID
      if (options.inviterId) {
        uni.setStorageSync('inviterId', options.inviterId)
        console.log('创始人介绍存储邀请人ID:', options.inviterId)
      }
    },
    // 微信小程序分享功能
    onShareAppMessage(res) {
      const currentUser = this.$store.state.userInfo
      const inviterId = currentUser?.userId || ''

      return {
        title: '忻自良：中医急救领域的传奇人物',
        path: `/pageA/xin_intro/index?inviterId=${inviterId}`,
        imageUrl: 'https://drxin-1359893946.cos.ap-shanghai.myqcloud.com/drxin-project/dev/share_logo.png'
      }
    },
    // 分享到朋友圈
    onShareTimeline() {
      const currentUser = this.$store.state.userInfo
      const inviterId = currentUser?.userId || ''

      return {
        title: '忻自良：中医急救领域的传奇人物',
        query: `inviterId=${inviterId}`,
        imageUrl: 'https://drxin-1359893946.cos.ap-shanghai.myqcloud.com/drxin-project/dev/share_logo.png'
      }
    }
  }
</script>
  <style scoped>
  .page-container {
    padding: 24rpx;
    background-color: #f6f8fb;
  }
  .title {
    font-size: 40rpx;
    font-weight: bold;
    color: #1a73e8;
    margin-bottom: 24rpx;
  }
  .section {
    margin-bottom: 32rpx;
  }
  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 16rpx;
    border-left: 8rpx solid #1a73e8;
    padding-left: 16rpx;
  }
  .paragraph {
    font-size: 28rpx;
    color: #555;
    line-height: 1.8;
  }
  .video-wrapper {
    margin: 24rpx 0;
    background-color: #000;
    border-radius: 16rpx;
  }
  </style>