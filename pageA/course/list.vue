<template>
  <view class="page-container">
    <Navbar :hideBtn="false" bgColor="#f3f4f6" title="课程列表" :fixed="false"></Navbar>

    <!-- 搜索栏 -->
    <view class="search-bar">
      <u-search
          v-model="searchValue"
          placeholder="搜索课程名称"
          :show-action="true"
          action-text="搜索"
          @search="onSearch"
          @custom="onSearch"
          bg-color="#ffffff"
      />
    </view>

    <!-- 课程列表 -->
    <view class="list-container">
      <DataList
          ref="dataList"
          :labels="labels"
          :data="listData"
          :loading="loading"
          :pagination="pagination"
          @refresh="onRefresh"
          @load-more="onLoadMore"
          @item-click="onItemClick"
      />
    </view>
  </view>
</template>

<script>
import Navbar from "@/components/navbar/Navbar";
import DataList from "@/components/data-list/DataList";

import { getCourseList } from "@/api/work/course";
// import {getDicts} from "@/api/system/dict/data";

export default {
  components: { Navbar, DataList },
  data() {
    return {
      labels: [
        { label: '课程名称', prop: 'courseName' },
        { label: '开始时间', prop: 'startTime' },
        { label: '结束时间', prop: 'endTime' },
        {label: '报名截止', prop: 'enrollDeadline'},
        { label: '地点', prop: 'location' },
        { label: '报名情况', prop: 'enrollment' },
        // { label: '状态', prop: 'courseStatusLabel' }
      ],
      listData: [],
      loading: false,
      pagination: {
        page: 1,
        pageSize: 5,
        total: 0,
      },
      searchValue: '',
      courseStatusOptions: [],
    };
  },
  onLoad(options) {
    // 缓存邀请人ID
    if (options.inviterId) {
      uni.setStorageSync('inviterId', options.inviterId)
    }
  },
  onShow() {
    this.pagination.page = 1;
    this.getList();
  },
  methods: {
    async getList(append = false) {
      this.loading = true;
      try {
        const res = await getCourseList({
          pageNum: this.pagination.page,
          pageSize: this.pagination.pageSize,
          courseName: this.searchValue,
        });

        const rows = res.rows.map(item => {
          return {
            ...item,
            // 组合报名情况字段
            enrollment: `${item.currentQuota}/${item.maxQuota}人`,
          };
        });

        if (append) {
          this.listData = [...this.listData, ...rows];
        } else {
          this.listData = rows;
        }

        this.pagination.total = res.total;
      } catch (error) {
        console.error('获取课程列表失败:', error);
        uni.showToast({ title: '加载失败', icon: 'none' });
      } finally {
        this.loading = false;
        // 停止 DataList 组件的刷新状态
        if (this.$refs.dataList) {
          this.$refs.dataList.stopRefresh();
        }
      }
    },

    onItemClick(item) {
      uni.navigateTo({
        url: `/pageA/course/detail?id=${item.id}`
      });
    },

    onSearch() {
      this.pagination.page = 1;
      this.getList(false);
    },

    onRefresh() {
      this.pagination.page = 1;
      this.getList(false);
    },

    onLoadMore() {
      if (this.listData.length < this.pagination.total) {
        this.pagination.page++;
        this.getList(true);
      }
    }
  },
  // 微信小程序分享功能
  onShareAppMessage(res) {
    const currentUser = this.$store.state.userInfo
    const inviterId = currentUser?.userId || ''

    return {
      title: '忻道-中医生命急救课程',
      path: `/pageA/course/list?inviterId=${inviterId}`,
      imageUrl: 'https://drxin-1359893946.cos.ap-shanghai.myqcloud.com/drxin-project/dev/share_logo.png'
    }
  },
  // 分享到朋友圈
  onShareTimeline() {
    const currentUser = this.$store.state.userInfo
    const inviterId = currentUser?.userId || ''

    return {
      title: '忻道-中医生命急救课程',
      query: `inviterId=${inviterId}`,
      imageUrl: 'https://drxin-1359893946.cos.ap-shanghai.myqcloud.com/drxin-project/dev/share_logo.png'
    }
  }
};
</script>

<style lang="scss" scoped>
.page-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
}

.search-bar {
  padding: 20rpx 30rpx;
  background-color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.03);
  flex-shrink: 0;
}

.list-container {
  flex: 1;
  padding: 20rpx 30rpx;
}

/* 自定义列表项样式 */
:deep(.apply-list-item) {
  border-radius: 16rpx !important;
  box-shadow: 0 6rpx 18rpx rgba(0, 0, 0, 0.05) !important;
  margin-bottom: 24rpx !important;
  border-left: 8rpx solid #2f80ed;
}

:deep(.apply-list-row) {
  margin-bottom: 12rpx !important;
}

:deep(.apply-list-label) {
  color: #666 !important;
  font-weight: 500 !important;
  min-width: 140rpx !important;
}

/* 状态标签样式 - 修复后的兼容写法 */
:deep(.status-tag) {
  display: inline-block;
  padding: 4rpx 16rpx;
  border-radius: 24rpx;
  font-size: 24rpx;
  font-weight: 500;

  &.status-enrolling {
    background-color: #e6f7ff;
    color: #1890ff;
  }

  &.status-full {
    background-color: #fff2f0;
    color: #f5222d;
  }

  &.status-ended {
    background-color: #f5f5f5;
    color: #8c8c8c;
  }
}
</style>