<template>
  <view class="course-detail">
    <Navbar :hideBtn="false" bgColor="transparent" title="课程详情"></Navbar>

    <!-- 课程头部卡片 -->
    <view class="course-header">
      <view class="course-badge">热门课程</view>
      <view class="course-title">{{ course.courseName }}</view>
      <view class="course-subtitle">提升专业技能，掌握实用技巧</view>

      <!-- 快速信息栏 -->
      <view class="quick-info">
        <view class="info-item">
          <view class="info-icon">
            <u-icon name="calendar" color="#fff" :size="iconSize"></u-icon>
          </view>
          <view class="info-label">时长</view>
          <view class="info-value">{{duration}}</view>
        </view>
        <view class="info-item">
          <view class="info-icon">
            <u-icon name="account" color="#fff" :size="iconSize"></u-icon>
          </view>
          <view class="info-label">名额</view>
          <view class="info-value">{{ course.currentQuota }}/{{ course.maxQuota }}</view>
        </view>
        <view class="info-item">
          <view class="info-icon">
            <u-icon name="map" color="#fff" :size="iconSize"></u-icon>
          </view>
          <view class="info-label">线下</view>
          <view class="info-value">面授</view>
        </view>
      </view>
    </view>

    <!-- 课程详细信息 -->
    <view class="course-info-section">
      <view class="section-header">
        <view class="section-title">
          <u-icon name="list" color="#2f80ed" :size="iconSize"></u-icon>
          <text>课程信息</text>
        </view>
      </view>

      <view class="info-cards">
        <view class="info-card">
          <view class="card-icon">
            <u-icon name="clock" color="#2f80ed" :size="iconSize"></u-icon>
          </view>
          <view class="card-content">
            <view class="card-label">开始时间</view>
            <view class="card-value">{{ course.startTime }}</view>
          </view>
        </view>

        <view class="info-card">
          <view class="card-icon">
            <u-icon name="clock" color="#2f80ed" :size="iconSize"></u-icon>
          </view>
          <view class="card-content">
            <view class="card-label">结束时间</view>
            <view class="card-value">{{ course.endTime }}</view>
          </view>
        </view>

        <view class="info-card">
          <view class="card-icon">
            <u-icon name="map" color="#2f80ed" :size="iconSize"></u-icon>
          </view>
          <view class="card-content">
            <view class="card-label">上课地址</view>
            <view class="card-value">{{ course.location }}</view>
          </view>
        </view>

        <view class="info-card deadline-card" :class="{ 'urgent': !isDeadlinePassed }">
          <view class="card-icon">
            <u-icon name="bell" :color="isDeadlinePassed ? '#999' : '#e74c3c'" :size="iconSize"></u-icon>
          </view>
          <view class="card-content">
            <view class="card-label">报名截止</view>
            <view class="card-value">{{ course.enrollDeadline }}</view>
          </view>
          <view class="urgency-indicator" v-if="!isDeadlinePassed">
            <view class="pulse-dot"></view>
            <text>即将截止</text>
          </view>
        </view>
      </view>

      <!-- 报名进度 -->
      <view class="enrollment-progress">
        <view class="progress-header">
          <text class="progress-title">报名进度</text>
          <text class="progress-count">{{ course.currentQuota }}/{{ course.maxQuota }}人</text>
        </view>
        <view class="progress-bar">
          <view class="progress-fill" :style="{ width: progressPercent + '%' }"></view>
        </view>
        <view class="progress-status" :class="{ 'full': isFull }">
          <u-icon v-if="isFull" name="close-circle-fill" color="#e74c3c" :size="iconSize"></u-icon>
          <u-icon v-else name="checkmark-circle-fill" color="#2f80ed" :size="iconSize"></u-icon>
          <text v-if="isFull"> 名额已满</text>
          <text v-else> 还有{{ course.maxQuota - course.currentQuota }}个名额</text>
        </view>
      </view>
    </view>

    <!-- 课程内容 -->
    <view class="course-content-section">
      <view class="section-header">
        <view class="section-title">
          <u-icon name="bookmark" color="#2f80ed" :size="iconSize"></u-icon>
          <text>课程内容</text>
        </view>
      </view>
      <view class="content-card">
        <rich-text :nodes="course.description" class="rich-content" />
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="footer-section">
      <view class="price-info">
        <view class="price-row">
          <text class="price-label">课程费用</text>
          <text class="price-value" v-if="course.coursePrice > 0">
            ￥{{ formatPrice(course.coursePrice) }}
          </text>
          <text class="price-value" v-else>免费</text>
        </view>
        <view class="price-tip highlight">
          <text>一次缴费 · 永久免费复训</text>
        </view>
      </view>
      <!-- 替换原 u-button 显示部分即可 -->
      <view class="button-group">
        <u-button
            v-if="isEnrolled && canCancelEnrollment"
            :custom-style="cancelButton"
            class="cancel-btn"
            @click="handleCancelEnroll"
        >
          <view class="button-content">
            <text class="button-text">取消报名</text>
          </view>
        </u-button>

        <u-button
            v-else-if="isEnrolled && !canCancelEnrollment"
            :custom-style="enrolledButton"
            class="enrolled-status"
            disabled
        >
          <view class="button-content">
            <u-icon name="checkmark-circle-fill" color="#52c41a" :size="16"></u-icon>
            <text class="button-text">已报名</text>
          </view>
        </u-button>

        <u-button
            v-else
            :custom-style="enrollButton"
            :class="{ 'disabled': isFull || isDeadlinePassed, 'primary': !isFull && !isDeadlinePassed }"
            :disabled="isFull || isDeadlinePassed"
            @click="handleEnrollNeedLogin"
        >
          <view class="button-content">
            <text class="button-text">{{ enrollButtonText }}</text>
          </view>
        </u-button>
      </view>

    </view>

    <!-- 添加注册弹窗组件 -->
    <RegisterPopup
        :show="showRegisterPopup"
        @close="closeRegisterPopup"
        @submit="handleRegisterSubmit"
    />
  </view>
</template>

<script>
import Navbar from "@/components/navbar/Navbar.vue";
import {getCourseDetail, enrollCourse, checkEnrollment, cancelEnrollment} from "@/api/work/course";
import { loginMixin } from '@/mixins/loginMixin'
import RegisterPopup from "@/components/register-popup/RegisterPopup"

export default {
  components: {Navbar,RegisterPopup},
  mixins: [loginMixin],
  data() {
    return {
      iconSize: 20,
      course: {},
      enrollButton: {
        width: '320rpx',
        height: '96rpx',
        fontSize: '32rpx',
        borderRadius: '48rpx',
        border: '1rpx solid #2f80ed',
        backgroundColor: '#2f80ed',
        color: '#fff',
        cursor: 'pointer',
        transition: 'background-color 0.3s ease'
      },
      enrolledButton: {
        width: '200rpx',
        height: '80rpx',
        fontSize: '28rpx',
        borderRadius: '40rpx',
        border: '1rpx solid #52c41a',
        backgroundColor: '#f6ffed',
        color: '#52c41a',
        cursor: 'default'
      },
      cancelButton: {
        width: '160rpx',
        height: '80rpx',
        fontSize: '28rpx',
        borderRadius: '40rpx',
        border: '1rpx solid #ff4d4f',
        backgroundColor: '#fff',
        color: '#ff4d4f',
        cursor: 'pointer',
        transition: 'background-color 0.3s ease'
      },
      courseId: null, // 用于存储课程ID
      isEnrolled: false, // 替代 isHave
    };
  },
  computed: {
    isDeadlinePassed() {
      if (!this.course.enrollDeadline) return false; // 如果没有截止时间，默认未截止
      // 将 "2025-06-18 23:59" 转换为 "2025/06/18 23:59" 以兼容iOS
      const deadline = this.course.enrollDeadline.replace(/(\d{4})-(\d{2})-(\d{2})/, '$1/$2/$3');
      return new Date() > new Date(deadline);
    },
    isFull() {
      return this.course.currentQuota >= this.course.maxQuota;
    },
    progressPercent() {
      return (this.course.currentQuota / this.course.maxQuota) * 100;
    },
    enrollButtonText() {
      if (this.isFull) return '已满员';
      if (this.isDeadlinePassed) return '已截止';
      return '立即报名';
    },
    // 判断是否可以取消报名（课程开始前24小时内不能取消）
    canCancelEnrollment() {
      if (!this.course.startTime) return true;
      // iOS兼容性处理
      const startTime = this.course.startTime.replace(/(\d{4})-(\d{2})-(\d{2})/, '$1/$2/$3');
      const courseStart = new Date(startTime);
      const now = new Date();
      const timeDiff = courseStart.getTime() - now.getTime();
      const hoursDiff = timeDiff / (1000 * 60 * 60);

      return hoursDiff > 8; // 课程开始前24小时以上可以取消
    },
    duration() {
      try {
        if (!this.course.startTime || !this.course.endTime) return "未知"; // 如果没有时间信息，返回默认值
        // iOS兼容性处理
        const formatDate = (dateStr) => dateStr.replace(/-/g, '/');

        const start = new Date(formatDate(this.course.startTime));
        const end = new Date(formatDate(this.course.endTime));

        // 检查是否为同一天
        const isSameDay = start.getDate() === end.getDate() &&
            start.getMonth() === end.getMonth() &&
            start.getFullYear() === end.getFullYear();

        if (isSameDay) {
          // 计算小时差（毫秒）
          const diff = end.getTime() - start.getTime();
          const hours = Math.ceil(diff / (1000 * 60 * 60)); // 向上取整
          return `${hours}小时`;
        } else {
          // 计算天数差（向上取整）
          const diff = end.getTime() - start.getTime();
          const days = Math.ceil(diff / (1000 * 60 * 60 * 24));
          return `${days}天`;
        }
      } catch (e) {
        console.error("计算时长出错:", e);
        return "3小时"; // 出错时返回默认值
      }
    },
  },
  onLoad(options) {
    // 缓存邀请人ID
    if (options.inviterId) {
      uni.setStorageSync('inviterId', options.inviterId)
      console.log('课程详情存储邀请人ID:', options.inviterId)
    }

    this.courseId = options.id
    if (options.scene) {
      this.scene = decodeURIComponent(options.scene)
      this.courseId = this.scene.split('=')[1]
    }
    this.getCourseDetail(this.courseId)
  },
  methods: {
    getCourseDetail(courseId) {
      // 这里可以调用接口获取课程详情
      getCourseDetail(courseId).then(response => {
        this.course = response.data;
        this.checkEnrollmentStatus(); // 课程加载完再检查是否已报名
      }).catch(error => {
        console.error("获取课程详情失败:", error);
        uni.showToast({
          title: '加载课程信息失败',
          icon: 'none'
        });
      });
    },
    async handleEnrollNeedLogin() {
      await this.requireLogin(async () => {
        await this.checkEnrollmentStatus(); // 登录成功后立即检查报名状态
        this.handleEnroll()
      }, {
        content: '此操作需要登录，是否立即登录？'
      })
    },
    handleEnroll() {
      // 检查是否已报名、名额是否已满或截止时间是否已过
      if (this.isEnrolled) {
        uni.showToast({
          title: '您已报名该课程',
          icon: 'none'
        });
        return;
      }
      if (this.isFull || this.isDeadlinePassed) return;
      // 调用报名接口
      const data = {
        courseId: this.course.id,
        userId: this.$store.state.userInfo.userId // 假设用户ID存储在本地
      };
      enrollCourse(data).then(res => {
        uni.showToast({
          title: '报名成功',
          icon: 'success'
        });
        this.isEnrolled = true;
        // 更新课程信息
        this.getCourseDetail(this.course.id);
      }).catch(error => {
        console.error("报名失败:", error);
        uni.showToast({
          title: error.msg,
          icon: 'error'
        });
      });
    },

    // 新增取消报名方法
    handleCancelEnroll() {
      // 检查是否可以取消报名
      if (!this.canCancelEnrollment) {
        uni.showToast({
          title: '课程开始前24小时内不能取消报名',
          icon: 'none'
        });
        return;
      }

      // 显示确认对话框
      uni.showModal({
        title: '确认取消报名',
        content: '确定要取消报名吗？取消后可能需要重新排队报名。',
        showCancel: true,
        cancelText: '我再想想',
        confirmText: '确定取消',
        confirmColor: '#ff4d4f',
        success: (res) => {
          if (res.confirm) {
            this.cancelEnrollment();
          }
        }
      });
    },

    // 取消报名接口调用
    cancelEnrollment() {
      const data = {
        courseId: this.course.id,
        userId: this.$store.state.userInfo.userId
      };

      uni.showLoading({
        title: '正在取消报名...'
      });

      cancelEnrollment(data).then(res => {
        uni.hideLoading();
        uni.showToast({
          title: '取消报名成功',
          icon: 'success'
        });
        this.isEnrolled = false;
        // 更新课程信息
        this.getCourseDetail(this.course.id);
      }).catch(error => {
        uni.hideLoading();
        console.error("取消报名失败:", error);
        uni.showToast({
          title: error.msg || '取消报名失败',
          icon: 'error'
        });
      });
    },

    async checkEnrollmentStatus() {
      const userId = this.$store.state.userInfo?.userId;
      if (!userId || !this.course.id) return;

      try {
        const res = await checkEnrollment({
          courseId: this.course.id,
          userId: userId
        });
        this.isEnrolled = res.data === true;
      } catch (error) {
        console.error("检查报名状态失败:", error);
        uni.showToast({
          title: '检查报名状态失败',
          icon: 'none'
        });
      }
    },
    formatPrice(price) {
      // 确保处理各种输入类型
      const value = parseFloat(price);
      return isNaN(value) ? '0.00' : value.toFixed(2);
    },
  },
  // 微信小程序分享功能
  onShareAppMessage(res) {
    const currentUser = this.$store.state.userInfo
    const inviterId = currentUser?.userId || ''

    return {
      title: this.course.title || '忻道课程',
      path: `/pageA/course/detail?id=${this.courseId}&inviterId=${inviterId}`,
      imageUrl: 'https://drxin-1359893946.cos.ap-shanghai.myqcloud.com/drxin-project/dev/share_logo.png'
    }
  },
  // 分享到朋友圈
  onShareTimeline() {
    const currentUser = this.$store.state.userInfo
    const inviterId = currentUser?.userId || ''

    return {
      title: this.course.title || '忻道课程',
      query: `id=${this.courseId}&inviterId=${inviterId}`,
      imageUrl: 'https://drxin-1359893946.cos.ap-shanghai.myqcloud.com/drxin-project/dev/share_logo.png'
    }
  }
};
</script>

<style scoped>
.course-detail {
  min-height: 100vh;
  background-color: #f5faff;
  padding-bottom: 120rpx;
}

/* 课程头部 */
.course-header {
  background-color: #2f80ed;
  padding: 40rpx 32rpx 80rpx; /* 增加底部内边距给浮层留空间 */
  color: white;
  position: relative;
  border-bottom-left-radius: 32rpx;
  border-bottom-right-radius: 32rpx;
  z-index: 1;
}

.course-badge {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.4);
  padding: 8rpx 24rpx;
  border-radius: 32rpx;
  font-size: 24rpx;
  display: inline-block;
  margin-bottom: 24rpx;
}

.course-title {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.course-subtitle {
  font-size: 20rpx;
  opacity: 0.95;
}

/* 快速信息栏 */
.quick-info {
  display: flex;
  justify-content: space-between;
  background-color: rgba(255, 255, 255, 0.15);
  border-radius: 16rpx;
  padding: 24rpx;
  margin-top: 32rpx;
}

.info-item {
  flex: 1;
  text-align: center;
  color: white;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.info-icon {
  margin-bottom: 8rpx;
}

/* 信息块 */
.course-info-section {
  position: relative;
  background: white;
  margin: -60rpx 32rpx 32rpx; /* 负margin使其浮于头部上方 */
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  z-index: 2;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2f80ed;
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.info-card {
  display: flex;
  align-items: center;
  background: #f2f4f8;
  border-left: 6rpx solid #2f80ed;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-top: 16rpx;
  position: relative;
}

.card-icon {
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-label {
  font-size: 26rpx;
  color: #666;
}

.card-value {
  font-size: 24rpx;
  font-weight: 600;
  color: #333;
  margin-left: auto;
}

/* 报名进度条 */
.enrollment-progress {
  margin-top: 32rpx;
}

.progress-bar {
  width: 100%;
  height: 20rpx;
  background: #e0e0e0;
  border-radius: 10rpx;
  overflow: hidden;
  margin-top: 12rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(to right, #56ccf2, #2f80ed);
  border-radius: 10rpx;
  transition: width 0.3s ease;
}

/* 进度状态 */
.progress-status {
  text-align: center;
  margin-top: 12rpx;
  font-size: 24rpx;
  color: #2f80ed;
  display: flex;
  justify-content: center;
  align-items: center;
}

.progress-status.full {
  color: #e74c3c;
}

/* 富文本区域 */
.course-content-section {
  margin-top: 40rpx;
}

.rich-content {
  font-size: 24rpx;
  line-height: 1.6;
  color: #333;
}

/* 底部按钮区 */
.footer-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.05);
  padding: 24rpx 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 10;
}

.price-label {
  font-size: 24rpx;
  color: #999;
}

.price-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #FF0000;
  margin-left: 8rpx;
}

.price-tip {
  display: flex;
  align-items: center;
  margin-top: 8rpx;
  font-size: 24rpx;
  color: #666;
}

.highlight {
  color: #52c41a;
  font-weight: 500;
}

/* 按钮组样式 */
.button-group {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.enrolled-buttons {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.enroll-button-container .u-button.primary,
.button-group .u-button.primary {
  background: #2f80ed;
  color: white;
}

.enroll-button-container .u-button.disabled,
.button-group .u-button.disabled {
  background: #ccc;
  color: white;
  cursor: not-allowed;
}

.enrolled-status {
  background: #f6ffed !important;
  border-color: #52c41a !important;
  color: #52c41a !important;
}

.cancel-btn {
  background: #fff !important;
  border-color: #ff4d4f !important;
  color: #ff4d4f !important;
}

.cancel-btn:hover {
  background: #fff2f0 !important;
}

/* 动态提示 */
.urgency-indicator {
  display: flex;
  align-items: center;
  color: #e74c3c;
  font-size: 24rpx;
  margin-top: 8rpx;
  position: absolute;
  right: 24rpx;
  bottom: 16rpx;
}

.pulse-dot {
  width: 12rpx;
  height: 12rpx;
  background: #e74c3c;
  border-radius: 50%;
  margin-right: 8rpx;
  animation: pulse 1.2s infinite ease-in-out;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.9;
  }
  50% {
    transform: scale(1.5);
    opacity: 0.5;
  }
  100% {
    transform: scale(1);
    opacity: 0.9;
  }
}

/* 按钮内容 */
.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}
</style>