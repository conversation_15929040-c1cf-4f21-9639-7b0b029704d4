<template>
  <view>
    <Navbar :hideBtn="false" bgColor="#f3f4f6"></Navbar>
    <view style="background-color: #2b85e4; padding: 40rpx;">
      <view class="avatar-upload-container">
        <u-avatar :src="userInfo.avatar || DEFAULT_AVATAR" size="120rpx" style="margin: 10rpx;"></u-avatar>
        <button class="avatar-upload-btn" open-type="chooseAvatar" @chooseavatar="onChooseAvatar"></button>
        <view class="avatar-tip">点击头像更换</view>
        <view v-if="isUploading" class="upload-loading">
          <u-loading-icon mode="circle" size="28" color="#ffffff"></u-loading-icon>
          <text class="loading-text">上传中...</text>
        </view>
      </view>
    </view>
    <view style="padding: 40rpx;">
      <u--form :model="userInfo" ref="uForm" labelWidth="160rpx" labelAlign="left" :rules="rules">
        <u-form-item label="昵称" prop="nickName" class="u-border-bottom">
          <u--input
            placeholder="请输入昵称"
            border="none"
            v-model="userInfo.nickName"
            type="nickname"
            clearable
          ></u--input>
        </u-form-item>
        <u-form-item label="邮箱" prop="email" class="u-border-bottom">
          <u--input
            placeholder="请输入内容"
            border="none"
            v-model="userInfo.email"
          ></u--input>
        </u-form-item>
      </u--form>
    </view>
    <view style="padding: 40rpx;">
      <u-row gutter="32">
        <u-col span="6">
          <u-button icon="arrow-left" text="返回" plain @click="goBack()"></u-button>
        </u-col>
        <u-col span="6">
          <u-button icon="checkmark-circle" text="保存" type="primary" @click="saveProfile"></u-button>
        </u-col>
      </u-row>
    </view>

    <!-- 修改密码弹窗 -->
    <Password :show="showPassword" @close="showPassword = false"></Password>
  </view>
</template>

<script>
import Navbar from '@/components/navbar/Navbar'
import storage from '@/utils/storage'
import { ACCESS_TOKEN } from '@/store/mutation-types'
import { updateInfo } from '@/api/user'
import Password from './password.vue'

const { environment } = require('@/config/environment.js')
const baseUrl = environment.baseURL

export default {
  components: {
    Navbar,
    Password
  },
  data () {
    return {
      DEFAULT_AVATAR: 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0',
      uploadStatus: false,
      isUploading: false,
      showPassword: false,
      userInfo: {
        nickName: '',
        email: '',
        phonenumber: '',
        avatar: ''
      },
      rules: {
        nickName: [
          { required: true, message: '请输入昵称', trigger: ['blur', 'change'] }
        ],
        email: [
          { type: 'email', message: '邮箱格式不正确', trigger: ['blur', 'change'] }
        ]
      }
    }
  },
  created() {
    // 获取当前登录用户信息
    this.$store.dispatch('Info').then(res => {
      this.userInfo = res.user || res
      this.uploadStatus = !!this.userInfo.avatar
    })
  },
  methods: {
    goBack () {
      uni.navigateBack({ delta: 1});
    },
    onChooseAvatar(e) {
      this.userInfo.avatar = e.detail.avatarUrl
      // 上传头像
      this.uploadAvatar()
    },
    /**
     * 通用上传文件方法
     * @param {String} filePath - 本地文件路径
     * @param {String} uploadUrl - 上传接口地址
     * @param {Object} formData - 额外表单参数
     * @returns {Promise} resolve(服务器返回结果)
     */
    uploadFile(filePath, uploadUrl = '/common/upload', formData = {}) {
      return new Promise((resolve, reject) => {
        uni.uploadFile({
          url: baseUrl + uploadUrl,
          header: {
            'Authorization': 'Bearer ' + storage.get(ACCESS_TOKEN)
          },
          filePath: filePath,
          name: 'file', // 后端接收字段名
          formData: formData,
          success: (res) => {
            let data = res.data
            // 将字符串转换为JSON对象
            data = typeof data === 'string' ? JSON.parse(data) : data
            if (data.code === 200) {
              this.uploadStatus = true
              resolve(data)
            } else {
              uni.showToast({ title: '上传失败', icon: 'error' })
              reject(data)
            }
          },
          fail: (err) => {
            uni.showToast({ title: '上传失败', icon: 'error' })
            reject(err)
          }
        })
      })
    },
    async uploadAvatar() {
      if (!this.userInfo.avatar) return
      this.isUploading = true
      try {
        const result = await this.uploadFile(this.userInfo.avatar, '/common/tencent-cos/upload')
        if (result.url) {
          this.userInfo.avatar = result.url
          uni.showToast({ title: '头像上传成功', icon: 'success' })
        }
      } catch (e) {
        // 错误已在 uploadFile 里处理
      } finally {
        this.isUploading = false
      }
    },
    saveProfile() {
      // 表单验证
      this.$refs.uForm.validate().then(valid => {
        if (!valid) {
          uni.showToast({ title: '请填写必填项', icon: 'none' })
          return
        }
        // 只取需要的字段
        const { nickName, avatar, email, phonenumber, userId } = this.userInfo
        const updateData = { nickName, avatar, email, phonenumber, userId }
        // 检查头像是否上传
        if (!this.uploadStatus) {
          uni.showToast({ title: '请上传头像', icon: 'error' })
          return
        }
        updateInfo(updateData).then(res => {
          uni.showToast({ title: '保存成功', icon: 'success' })
        }).catch(() => {
          uni.showToast({ title: '保存失败', icon: 'none' })
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.avatar-upload-container {
  position: relative;
  width: 140rpx;
  height: 140rpx;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.avatar-upload-btn {
  position: absolute;
  left: 0; top: 0; right: 0; bottom: 0;
  width: 100%; height: 100%;
  background: transparent;
  border: none;
  padding: 0;
  margin: 0;
  opacity: 0;
  z-index: 2;
}
.avatar-tip {
  margin-top: 8rpx;
  color: #fff;
  font-size: 22rpx;
  text-align: center;
  opacity: 0.8;
}
.upload-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  z-index: 3;
}
.loading-text {
  color: #fff;
  font-size: 24rpx;
  margin-top: 10rpx;
}
</style>
