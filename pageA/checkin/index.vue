<template>
    <view class="checkin-container">
      <!-- 使用Navbar组件替换状态栏 -->
      <Navbar title="课程签到" :autoBack="true" :placeholder="true" bgColor="#ffffff" :border="true" />
  
      <!-- 加载状态 -->
      <view v-if="loading" class="loading-container">
        <u-loading-icon mode="flower" size="40"></u-loading-icon>
        <text class="loading-text">正在加载课程信息...</text>
      </view>
  
      <!-- 主要内容 -->
      <view v-else class="main-content">
        <!-- 课程卡片 -->
        <view class="course-card">
          <view class="course-info">
            <text class="course-name">{{ courseInfo.courseName || '加载中...' }}</text>
            <view class="course-details">
              <view class="detail-item">
                <u-icon name="clock" size="16" color="#666"></u-icon>
                <text>开始：{{ formattedStartTime }}</text>
              </view>
              <view class="detail-item">
                <u-icon name="clock" size="16" color="#666"></u-icon>
                <text>结束：{{ formattedEndTime }}</text>
              </view>
              <view class="detail-item">
                <u-icon name="map" size="16" color="#666"></u-icon>
                <text>地点：{{ courseInfo.location || '--' }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 用户信息卡片 -->
        <view v-if="userCheckinStatus">
          <UserInfoCard :user="userInfo" :isGuest="false" />
        </view>

        <!-- 签到按钮区域 -->
        <view class="checkin-section" :class="{ 'with-user-card': userCheckinStatus }">
          <view v-if="userCheckinStatus" class="checked-status">
            <view class="success-icon">
              <u-icon name="checkmark-circle-fill" size="48" color="#19be6b"></u-icon>
            </view>
            <text class="checked-text">您已成功签到此课程</text>
            <text class="checked-time">签到时间：{{ formattedCheckinTime }}</text>
            <view class="success-decoration"></view>
          </view>
          
          <view v-else class="checkin-actions">
            <view class="checkin-button-wrapper">
              <u-button 
                :loading="checkinLoading"
                :disabled="!canCheckin"
                type="primary" 
                size="large"
                shape="circle"
                :custom-style="checkinButtonStyle"
                @click="handleCheckinNeedLogin"
              >
                <view class="button-content">
                  <text class="button-main-text">{{ getCheckinButtonText() }}</text>
                  <text v-if="canCheckin" class="button-time-text">{{ currentTimeFormatted }}</text>
                </view>
              </u-button>
            </view>
            
            <view class="checkin-tips">
              <text v-if="!canCheckin && !isTimeValid" class="tip-text warning">
                <u-icon name="clock" size="14" color="#ff9900"></u-icon>
                {{ getTimeValidText() }}
              </text>
              <text v-else-if="canCheckin" class="tip-text success">
                <u-icon name="checkmark-circle" size="14" color="#19be6b"></u-icon>
                点击上方按钮完成签到
              </text>
            </view>
          </view>
        </view>
      </view>

      <!-- 错误提示 -->
      <view v-if="error" class="error-container">
        <u-icon name="close-circle" size="40" color="#fa3534"></u-icon>
        <text class="error-text">{{ error }}</text>
        <u-button type="primary" size="small" @click="retry">重试</u-button>
      </view>

      <!-- 添加注册弹窗组件 -->
      <RegisterPopup
          :show="showRegisterPopup"
          @close="closeRegisterPopup"
          @submit="handleRegisterSubmit"
      />

      <!-- 返回我的 悬浮按钮 -->
      <view class="floating-button">
        <u-button
          type="primary"
          shape="circle"
          :custom-style="returnButtonStyle"
          @click="goToMyPage"
        >
          <u-icon name="home" color="#fff" size="20"></u-icon>
        </u-button>
      </view>
    </view>
  </template>
  
  <script>
  import Navbar from '@/components/navbar/Navbar.vue'
  import UserInfoCard from '@/components/UserInfoCard/UserInfoCard.vue'
  import {loginMixin} from '@/mixins/loginMixin'
  import RegisterPopup from "@/components/register-popup/RegisterPopup"
  import {getCourseDetail, checkinStatus, performCheckin} from '@/api/work/course.js'
  import { checkWechatRegistered } from "@/api/invited_register/invitedRegister"
  import { mapActions } from 'vuex'

  export default {
    components: {
      Navbar,
      UserInfoCard,
      RegisterPopup
    },
    mixins: [loginMixin],
    data() {
      return {
        loading: true,
        error: '',
        checkinLoading: false,
        courseId: '',
        courseInfo: {},
        userCheckinStatus: null,
        currentTime: new Date(),
        userInfo: {}, // 用户信息
        showRegisterPopup: false
      }
    },
    
    computed: {
      // 是否为访客
      isGuest() {
        return this.userInfo?.guest !== false || !this.$store.state.userInfo?.userId
      },

      // 格式化后的开始时间
      formattedStartTime() {
        if (!this.courseInfo.startTime) return '--'
        return this.formatTime(this.courseInfo.startTime)
      },
      
      // 格式化后的结束时间
      formattedEndTime() {
        if (!this.courseInfo.endTime) return '--'
        return this.formatTime(this.courseInfo.endTime)
      },
      
      // 格式化后的签到时间
      formattedCheckinTime() {
        if (!this.userCheckinStatus || !this.userCheckinStatus.attendTime) return '--'
        return this.formatTime(this.userCheckinStatus.attendTime)
      },

      // 实时更新的当前时间，格式为 HH:mm:ss
      currentTimeFormatted() {
        const date = this.currentTime
        const hours = date.getHours().toString().padStart(2, '0')
        const minutes = date.getMinutes().toString().padStart(2, '0')
        const seconds = date.getSeconds().toString().padStart(2, '0')
        return `${hours}:${minutes}:${seconds}`
      },

      // 是否可以签到
      canCheckin() {
        return this.isTimeValid && !this.userCheckinStatus
      },
      
      // 时间是否有效
      isTimeValid() {
        if (!this.courseInfo.endTime) return false
        const now = this.currentTime.getTime()
        const end = new Date(this.formatDateForIOS(this.courseInfo.endTime)).getTime()
        return now <= end
      },
      
      // 签到按钮样式
      checkinButtonStyle() {
        return {
          borderRadius: '50%',
          width: '120px',
          height: '120px',
          background: this.canCheckin ? 'linear-gradient(135deg, #2F80ED 0%, #56CCF2 100%)' : '#c8c9cc',
          boxShadow: this.canCheckin ? '0 8px 25px rgba(47, 128, 237, 0.4)' : 'none',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: '18px',
          fontWeight: '600',
          color: '#ffffff',
          // 设置为列布局以容纳时间和文本
          flexDirection: 'column'
        }
      },

      // 返回按钮样式
      returnButtonStyle() {
        return {
          width: '50px',
          height: '50px',
          background: 'linear-gradient(135deg, #2F80ED 0%, #56CCF2 100%)',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
        }
      }
    },
    
    onLoad(options) {
      console.log('options:', options)
      // 获取课程ID（从扫码scene参数或直接传入）
      this.courseId = options.scene ? decodeURIComponent(options.scene).split("=")[1] : options.courseId
      
      if (this.courseId) {
        this.loadCourseInfo()
      } else {
        this.error = '课程信息获取失败，请重新扫码'
        this.loading = false
      }
      
      // 定时更新当前时间
      this.timeInterval = setInterval(() => {
        this.currentTime = new Date()
      }, 1000)
    },
    
    onShow() {
      // 自动登录逻辑：仅访客时
      (async () => {
        if (this.isGuest) {
          try {
            const loginRes = await this.getWxLoginCode()
            const { data } = await checkWechatRegistered({ code: loginRes.code })
            if (data.isExists) {
              let loginRes2 = await this.getWxLoginCode()
              await this.performLogin({
                code: loginRes2.code,
                loginType: 'weixin',
                openid: data.openid
              })
              // 登录成功后重新检查用户状态
              await this.checkUserStatus()
            }
          } catch (error) {
            console.error('自动登录失败:', error)
            // 自动登录失败不影响正常使用
          }
        }
      })()
    },

    onUnload() {
      if (this.timeInterval) {
        clearInterval(this.timeInterval)
      }
    },
    
    methods: {
      ...mapActions(['Info', 'Login']),

      async handleCheckinNeedLogin() {
        if(!this.$store.state.userInfo?.userId){
          await this.requireLogin(async () => {
            // 登录成功后，先检查一遍签到状态
            await this.checkUserStatus()

            // 直接调用签到。handleCheckin内部有!canCheckin的保护，
            // 如果上一步检查发现已签到，canCheckin会变为false，从而阻止重复签到。
            await this.handleCheckin()
          }, {
            content: '签到操作需要登录，是否立即登录？'
          })
        }else {
          await this.handleCheckin()
        }
      },

      // 获取签到按钮文本
      getCheckinButtonText() {
        if (this.checkinLoading) return '签到中...'
        if (!this.isTimeValid) return '签到已截止'
        return '签到'
      },
      
      // 加载课程信息
      async loadCourseInfo() {
        this.loading = true
        this.error = ''
        try {
          // 1. 先加载公开的课程信息，这步不依赖登录
          const response = await getCourseDetail(this.courseId)
          this.courseInfo = response.data

          // 2. 接着检查用户状态
          await this.checkUserStatus()
          
        } catch (error) {
          // 这个 catch 主要捕获 getCourseDetail 的失败
          this.error = error.msg || '加载课程信息失败'
        } finally {
          this.loading = false
        }
      },
      
      async checkUserStatus() {
        // 判断用户是否已登录 (使用可选链安全访问)
        const courseId = this.courseId
        const userId = this.$store.state.userInfo?.userId
        if (userId) {
          // 如果已登录，再检查该用户的签到状态
          try {
            const checkinResponse = await checkinStatus({courseId, userId})
            this.userCheckinStatus = checkinResponse.data
            
            // 如果已签到，从Store获取并显示用户信息 (使用可选链安全访问)
            if (this.userCheckinStatus) {
              this.userInfo = this.$store.state.userInfo
            }
          } catch (checkinError) {
            // 获取签到状态失败，可以降级处理，不阻塞主流程
            // 用户仍然可以看到签到按钮，并可以尝试签到
            console.error('获取签到状态失败:', checkinError)
          }
        }
      },
      
      // 处理签到
      async handleCheckin() {
        if (!this.canCheckin) return
        
        try {
          this.checkinLoading = true
          
          const checkinData = {
            courseId: this.courseId,
            userId: this.$store.state.userInfo?.userId,
            timestamp: new Date().getTime()
          }
          
          const response = await performCheckin(checkinData)
          
          // 签到成功
          this.userCheckinStatus = response.data
          this.userInfo = this.$store.state.userInfo
          
          // 显示成功提示
          uni.showToast({
            title: '签到成功！',
            icon: 'success'
          })
          
        } catch (error) {
          uni.showToast({
            title: error.msg || '签到失败，请重试',
            icon: 'none'
          })
        } finally {
          this.checkinLoading = false
        }
      },
      
      // 跳转到"我的"页面
      goToMyPage() {
        uni.switchTab({
          url: '/pages/center/index'
        })
      },
      
      // 重试
      retry() {
        this.loadCourseInfo()
      },
      
      // 获取时间有效性提示文本
      getTimeValidText() {
        const now = this.currentTime.getTime()
        if (!this.courseInfo.endTime) return ''
        const end = new Date(this.formatDateForIOS(this.courseInfo.endTime)).getTime()
        
        if (now > end) {
          return '已超过课程结束时间，无法签到'
        }
        return ''
      },
      
      // 格式化时间
      formatTime(timeStr) {
        if (!timeStr) return '--'
        const date = new Date(this.formatDateForIOS(timeStr))
        const year = date.getFullYear()
        const month = (date.getMonth() + 1).toString().padStart(2, '0')
        const day = date.getDate().toString().padStart(2, '0')
        const hours = date.getHours().toString().padStart(2, '0')
        const minutes = date.getMinutes().toString().padStart(2, '0')
        const seconds = date.getSeconds().toString().padStart(2, '0')
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
      },
      
      // 将日期格式转换为iOS兼容格式
      formatDateForIOS(dateStr) {
        if (!dateStr) return dateStr
        // 将 "yyyy-MM-dd HH:mm:ss" 转换为 "yyyy-MM-ddTHH:mm:ss"
        return dateStr.replace(' ', 'T')
      },

      // 获取微信登录code
      getWxLoginCode() {
        return new Promise((resolve, reject) => {
          uni.login({
            provider: "weixin",
            success: resolve,
            fail: reject
          })
        })
      },

      // 执行登录
      async performLogin(loginData) {
        try {
          await this.Login(loginData)
          await this.getUserInfo()
        } catch (error) {
          console.error('登录失败:', error)
        }
      },
      async getUserInfo() {
        try {
          await this.Info()
        } catch (error) {
          console.error('获取用户信息失败:', error)
          this.showToast('获取用户信息失败', 'error')
        }
      }
    }
  }
  </script>
  
  <style lang="scss" scoped>
  .checkin-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  }
  
  .loading-container, .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 100px 20px;
    
    .loading-text, .error-text {
      margin: 15px 0;
      color: #666;
      font-size: 14px;
    }
  }
  
  .main-content {
    padding: 20px 15px;
  }
  
  .course-card {
    background: #fff;
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    
    .course-info {
      .course-name {
        font-size: 22px;
        font-weight: 600;
        color: #333;
        display: block;
        margin-bottom: 20px;
        line-height: 1.3;
      }
      
      .course-details {
        display: flex;
        flex-direction: column;
        gap: 12px;
        
        .detail-item {
          display: flex;
          align-items: center;
          
          text {
            margin-left: 10px;
            font-size: 15px;
            color: #666;
          }
        }
      }
    }
  }
  
  .checkin-section {
    background: #fff;
    border-radius: 16px;
    padding: 40px 24px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    position: relative;
    overflow: hidden;
    
    .checked-status {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      
      .success-icon {
        margin-bottom: 16px;
      }
      
      .checked-text {
        display: block;
        font-size: 20px;
        font-weight: 600;
        color: #19be6b;
        margin-bottom: 8px;
      }
      
      .checked-time {
        font-size: 14px;
        color: #666;
      }
      
      .success-decoration {
        position: absolute;
        top: -20px;
        right: -20px;
        width: 100px;
        height: 100px;
        background: linear-gradient(135deg, rgba(25, 190, 107, 0.1), rgba(45, 183, 245, 0.1));
        border-radius: 50%;
        z-index: -1;
      }
    }
    
    .checkin-actions {
      .checkin-button-wrapper {
        margin-bottom: 20px;

        .button-content {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          line-height: 1.2;
        }

        .button-main-text {
          font-size: 24px;
          font-weight: 600;
        }

        .button-time-text {
          font-size: 14px;
          opacity: 0.8;
          margin-top: 4px;
        }
      }
      
      .checkin-tips {
        .tip-text {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 6px;
          font-size: 14px;
          
          &.success {
            color: #19be6b;
          }
          
          &.warning {
            color: #ff9900;
          }
        }
      }
    }
  }

  .floating-button {
    position: fixed;
    right: 20px;
    bottom: 100px;
    z-index: 10;
  }
  </style>