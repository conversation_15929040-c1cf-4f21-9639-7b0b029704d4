<template>
  <view>
    <Navbar title="设置" :hideBtn="false" bgColor="#ffffff" />
    
    <!-- 个人设置菜单 -->
    <view class="settings-container">
      <view class="settings-section">
        <view class="section-title">个人设置</view>

        <!-- 加载状态 -->
        <view v-if="menuLoading" class="loading-container">
          <u-loading-icon mode="circle" color="#2979ff" size="24"></u-loading-icon>
          <text class="loading-text">加载中...</text>
        </view>

        <!-- 菜单列表 -->
        <view v-else class="menu-list">
          <view
            v-for="item in personalSettings"
            :key="item.id"
            class="menu-item"
            @click="navigateTo(item.path)"
          >
            <view class="menu-item-left">
              <u-icon :name="item.icon" color="#2979ff" size="20"></u-icon>
              <text class="menu-title">{{ item.title }}</text>
            </view>
            <u-icon name="arrow-right" color="#c0c4cc" size="16"></u-icon>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import Navbar from '@/components/navbar/Navbar'
import { mapState } from 'vuex'
import { getWxMenu } from '@/api/system/menu'

export default {
  name: 'SettingsPage',
  components: {
    Navbar
  },

  data() {
    return {
      menuLoading: false,
      personalSettings: []
    }
  },

  computed: {
    ...mapState(['userInfo']),

    user() {
      return this.userInfo || {}
    }
  },

  onLoad() {
    (async () => {
      await this.loadMenuData()
    })()
  },

  methods: {
    // 加载菜单数据
    async loadMenuData() {
      if (this.menuLoading) return
      this.menuLoading = true

      try {
        const res = await getWxMenu("personal")
        if (res.code === 200 && res.data) {
          this.personalSettings = res.data
        } else {
          console.warn('获取设置菜单数据失败:', res.msg)
        }
      } catch (error) {
        console.error('加载设置菜单数据失败:', error)
        // 使用默认菜单作为降级方案
        this.setDefaultMenuItems()
      } finally {
        this.menuLoading = false
      }
    },

    // 页面导航
    navigateTo(url) {
      if (!url) return
      uni.navigateTo({
        url,
        fail: (err) => {
          console.error('页面跳转失败:', err)
          this.showToast('页面跳转失败', 'error')
        }
      })
    },

    // 统一的Toast提示
    showToast(title, icon = 'none') {
      uni.showToast({
        title,
        icon: icon === 'error' ? 'none' : icon,
        duration: 2000
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.settings-container {
  padding: 0;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.settings-section {
  background-color: #fff;
  overflow: hidden;
}

.section-title {
  padding: 30rpx 30rpx 20rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #303133;
  border-bottom: 1px solid #f0f0f0;
}

.menu-list {
  padding: 0;
}

.menu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s;

  &:last-child {
    border-bottom: none;
  }

  &:active {
    background-color: #f8f9fa;
  }
}

.menu-item-left {
  display: flex;
  align-items: center;
}

.menu-title {
  font-size: 30rpx;
  color: #303133;
  margin-left: 20rpx;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 30rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #909399;
  margin-top: 20rpx;
}
</style>
