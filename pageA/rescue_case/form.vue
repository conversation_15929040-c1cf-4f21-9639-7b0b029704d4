<template>
	<view class="container">
		<u-navbar :title="pageTitle" @leftClick="goBack" safeAreaInsetTop fixed placeholder></u-navbar>
		<view class="form-wrapper">
			<u--form :model="formModel" ref="uForm" :rules="rules" labelPosition="left" labelWidth="160rpx">
				<!-- 急救基本信息 -->
				<view class="form-section">
					<view class="section-title">急救基本信息</view>
					<u-form-item label="急救日期" prop="rescueDate" borderBottom @click="!isFormDisabled && (showDatePicker = true)" required>
						<u--input v-model="formModel.rescueDate" disabled disabledColor="#ffffff" placeholder="请选择急救日期"
							border="none"></u--input>
					</u-form-item>
					<u-form-item label="所在城市" prop="city" borderBottom required>
						<view @click="!isFormDisabled && showAddressPicker()" class="address-input" :class="{ 'disabled': isFormDisabled }">
							<text v-if="formModel.cityText" class="address-text">{{ formModel.cityText }}</text>
							<text v-else class="placeholder-text">请选择省市区</text>
							<u-icon name="arrow-right" :color="isFormDisabled ? '#c8c9cc' : '#c0c4cc'" size="14"></u-icon>
						</view>
						<u-picker
							:show="showLocal"
							:columns="addressColumns"
							@confirm="onAddressConfirm"
							@cancel="showLocal = false"
							@close="showLocal = false"
							@change="onAddressChange"
							title="请选择所在地"
							keyName="name"
							itemHeight="80"
							closeOnClickOverlay
							ref="uPicker"
							:defaultIndex="defaultAddress"
							immediateChange
						></u-picker>
					</u-form-item>
					<u-form-item label="详细地址" prop="address" borderBottom required>
						<u--input v-model="formModel.address" placeholder="请输入详细地址" border="none" :disabled="isFormDisabled"></u--input>
					</u-form-item>
				</view>

				<!-- 被救助人信息 -->
				<view class="form-section">
					<view class="section-title">被救助人信息</view>
					<u-form-item label="姓名" prop="patientName" borderBottom required>
						<u--input v-model="formModel.patientName" placeholder="请输入被救助人姓名" border="none" :disabled="isFormDisabled"></u--input>
					</u-form-item>
					<u-form-item label="性别" prop="patientGender" borderBottom required>
						<u-radio-group v-model="formModel.patientGender" placement="row" :disabled="isFormDisabled">
							<u-radio :customStyle="{marginRight: '16px'}" v-for="(item, index) in genderList"
								:key="index" :label="item.name" :name="item.value" :disabled="isFormDisabled">
							</u-radio>
						</u-radio-group>
					</u-form-item>
					<u-form-item label="年龄" prop="patientAge" borderBottom required>
						<u--input v-model="formModel.patientAge" type="number" placeholder="请输入被救助人年龄" border="none" :disabled="isFormDisabled">
						</u--input>
					</u-form-item>
				</view>

				<!-- 急救情况说明 -->
				<view class="form-section">
					<view class="section-title">急救情况说明</view>
					<u-form-item label="病症类型" prop="illnessType" borderBottom @click="!isFormDisabled && (showIllnessTypePicker = true)" required>
						<u--input v-model="illnessTypeText" disabled disabledColor="#ffffff" placeholder="请选择病症类型" border="none">
							<template #suffix>
								<u-icon name="arrow-down" size="18" :color="isFormDisabled ? '#c8c9cc' : '#909399'"/>
							</template>
						</u--input>
					</u-form-item>
					<u-form-item label="是否连线" prop="online_flag" borderBottom required>
						<u-radio-group v-model="formModel.online_flag" placement="row" :disabled="isFormDisabled" @change="onOnlineChange">
							<u-radio :customStyle="{marginRight: '16px'}" v-for="(item, index) in onlineOptions"
								:key="index" :label="item.name" :name="item.value" :disabled="isFormDisabled">
							</u-radio>
						</u-radio-group>
					</u-form-item>
					<u-form-item v-if="formModel.online_flag === '1'" label="连线员" prop="remoteGuideRealName" borderBottom @click="!isFormDisabled && openRemoteGuidePicker()" required>
						<view class="rescuer-select-input" :class="{ 'disabled': isFormDisabled }">
							<text v-if="formModel.remoteGuideRealName" class="rescuer-name-text">{{ formModel.remoteGuideRealName }}</text>
							<text v-else class="placeholder-text">点击选择连线员</text>
							<u-icon name="arrow-right" size="22" class="input-btn" :color="isFormDisabled ? '#c8c9cc' : '#909399'" />
						</view>
					</u-form-item>
					<u-form-item label="急救说明" prop="rescueDescription" borderBottom required>
						<u--textarea v-model="formModel.rescueDescription" placeholder="请描述施救过程" count border="bottom" maxlength="200" :disabled="isFormDisabled"></u--textarea>
					</u-form-item>
					<!-- 案例附件 -->
					<u-form-item label="案例附件">
						<view class="multi-photo-upload" :class="{ 'disabled': isFormDisabled }">
							<view v-for="(item, idx) in attachmentList" :key="getAttachmentKey(item, idx)" class="photo-thumb">
								<image v-if="item.fileType === 'image'" :src="item.fileUrl" mode="aspectFill" class="thumb-image" @click="previewAttachment(idx)"/>
								<view v-else-if="item.fileType === 'video'" class="video-thumb" @click="previewAttachment(idx)">
									<view class="video-icon">
										<u-icon name="play-circle" color="#fff" size="40"/>
									</view>
								</view>
								<view v-if="!isFormDisabled" class="delete-btn" @click.stop="deleteAttachment(idx)">
									<u-icon name="close" color="#ffffff" size="20"/>
								</view>
							</view>
							<view v-if="canAddImage && !isFormDisabled" class="add-photo-btn" @click="chooseImage">
								<u-icon name="camera-fill" size="40" color="#909399"/>
								<text class="upload-text">添加图片</text>
							</view>
							<view v-if="canAddVideo && !isFormDisabled" class="add-photo-btn" @click="chooseVideo">
								<u-icon name="play-right-fill" size="40" color="#909399"/>
								<text class="upload-text">添加视频</text>
							</view>
						</view>
						<view class="form-tips">仅支持图片和视频，图片≤5MB，视频≤50MB，总共最多{{ maxAttachmentCount }}个，视频最多{{ maxVideoCount }}个。</view>
					</u-form-item>
				</view>

				<!-- 救援人员列表 -->
				<view class="form-section">
					<view class="section-title">救援人员列表</view>
					<view class="rescuer-tips">请点击救援人员姓名字段选择人员，系统将自动填充手机号和身份证号</view>
					<view class="rescuer-list">
						<view class="rescuer-item rescuer-card" v-for="(rescuer, index) in formModel.rescuers" :key="index">
							<!-- 删除按钮 -->
							<view class="delete-btn" @click="removeRescuer(index)" v-if="formModel.rescuers.length > 1 && !isFormDisabled">
								<u-icon name="close" size="18" color="#ff4757"></u-icon>
							</view>
							<u-form-item :label="`救援人员${index + 1}姓名`" :prop="`rescuers.${index}.rescuerName`" :rules="rescuerNameRule" required @click="!isFormDisabled && openUserPicker(index)">
								<view class="rescuer-select-input" :class="{ 'disabled': isFormDisabled }">
									<text v-if="rescuer.rescuerName" class="rescuer-name-text">{{ rescuer.rescuerName }}</text>
									<text v-else class="placeholder-text">点击选择救援人员</text>
									<u-icon name="arrow-right" size="22" class="input-btn" :color="isFormDisabled ? '#c8c9cc' : '#909399'" />
								</view>
							</u-form-item>
							<u-form-item :label="`手机号`" :prop="`rescuers.${index}.rescuerPhone`" :rules="rescuerPhoneRule" required>
								<u--input v-model="rescuer.rescuerPhone" placeholder="请先选择救援人员" border="none" disabled disabledColor="#ffffff"></u--input>
							</u-form-item>
							<u-form-item :label="`身份证号`" :prop="`rescuers.${index}.rescuerIdCard`" :rules="rescuerIdCardRule" required>
								<u--input v-model="rescuer.rescuerIdCard" placeholder="请先选择救援人员" border="none" disabled disabledColor="#ffffff"></u--input>
							</u-form-item>
						</view>
					</view>
					<u-button icon="plus" type="primary" plain text="添加救援人员" @click="addRescuer" :disabled="isFormDisabled"></u-button>
				</view>
			</u--form>
		</view>

		<!-- 操作按钮 -->
		<view class="submit-button-wrapper" v-if="showActionButtons">
			<u-row gutter="32">
				<u-col span="6">
					<u-button text="保存" plain @click="onSave" :loading="saveLoading" :disabled="saveLoading || saveAndSubmitLoading" />
				</u-col>
				<u-col span="6">
					<u-button text="保存并提交" type="warning" @click="onSaveAndSubmit" :loading="saveAndSubmitLoading" :disabled="saveLoading || saveAndSubmitLoading" />
				</u-col>
			</u-row>
		</view>

		<!-- 状态提示 -->
		<view v-if="formModel.rescueStatus === '2'" class="status-tip reviewing">
			<u-icon name="clock-fill" color="#ff9500" size="24"/>
			<text class="status-text">案例审核中，请耐心等待</text>
		</view>

		<view v-if="formModel.rescueStatus === '3'" class="status-tip approved">
			<u-icon name="checkmark-circle-fill" color="#19be6b" size="24"/>
			<text class="status-text">案例已通过</text>
		</view>

		<view v-if="formModel.rescueStatus === '4'" class="status-tip rejected">
			<u-icon name="close-circle-fill" color="#fa3534" size="24"/>
			<text class="status-text">案例已驳回，请修改后重新提交</text>
		</view>


		<!-- Pickers -->
		<u-datetime-picker :show="showDatePicker" v-model="datePickerValue" mode="date" @confirm="onDateConfirm"
			@cancel="showDatePicker = false"></u-datetime-picker>

		<u-picker :show="showIllnessTypePicker"
			:columns="[illnessTypeOptions]"
			keyName="label"
			@confirm="onIllnessTypeConfirm"
			@cancel="showIllnessTypePicker = false"
			closeOnClickOverlay
			@close="showIllnessTypePicker = false"/>

		<UserSearchPopup
			:show="showUserPicker"
			:title="'搜索救援人员'"
			:userType="'aider,mentor,disciple'"
			@close="showUserPicker = false"
			@select="chooseUser"
		/>

		<UserSearchPopup
			:show="showRemoteGuidePicker"
			:title="'搜索连线员'"
			:userType="'mentor, disciple'"
			@close="showRemoteGuidePicker = false"
			@select="chooseRemoteGuide"
		/>

		<!-- Video Preview Modal -->
		<view v-if="showVideoPreview" class="video-preview-modal">
			<view class="video-preview-mask" @click="showVideoPreview = false"></view>
			<video :src="previewVideoUrl"
             :autoplay="false"
             :loop="false"
             referrer-policy="origin"
             :muted="false"
             objectFit="contain" controls class="video-preview-player"/>
			<view class="video-preview-close" @click="showVideoPreview = false">
				<u-icon name="close" color="#fff" size="36"/>
			</view>
		</view>

	</view>
</template>

<script>
	import areas from '@/utils/areas.json';
	import UserSearchPopup from '@/components/UserSearchPopup/UserSearchPopup';
	import { uploadImage } from '@/utils/upload.js';
	import { getDicts } from '@/api/system/dict/data.js';
	import {
		getRescueCaseInfo,
		saveRescueCase,
		updateRescueCase
	} from '@/api/center/rescue_case/rescue_case.js';

	export default {
		components: {
			UserSearchPopup,
		},
		data() {
			return {
				pageTitle: '急救案例申请表单',
				caseId: null,
				formModel: {
					rescueDate: '',
					city: '',
					cityText: '',
					address: '',
					patientName: '',
					patientGender: '男',
					patientAge: '',
					illnessType: '',
					rescueDescription: '',
                    rescueStatus: '1',
					online_flag: '0', // 是否连线，默认为否
					remoteGuideUserId: '', // 远程指导人ID
					remoteGuideRealName: '', // 远程指导人姓名
					rescuers: [{
						rescuerName: '',
						rescuerPhone: '',
						rescuerIdCard: '',
						rescuerId: null
					}],
					attachments: [], // 附件列表
				},
				rules: {
					rescueDate: {
						type: 'string',
						required: true,
						message: '请选择急救日期',
						trigger: ['blur', 'change']
					},
					city: {
						type: 'string',
						required: true,
						message: '请选择所在城市',
						trigger: ['change']
					},
					address: {
						type: 'string',
						required: true,
						message: '请输入详细地址',
						trigger: ['blur']
					},
					patientName: {
						type: 'string',
						required: true,
						message: '请输入被救助人姓名',
						trigger: ['blur']
					},
					patientGender: {
						type: 'string',
						required: true,
						message: '请选择性别',
						trigger: ['change']
					},
					patientAge: [{
						required: true,
						message: '请输入被救助人年龄',
						trigger: ['blur', 'change']
					}, {
						validator: (rule, value,) => {
							return uni.$u.test.range(value, [0, 120])
						},
						message: '年龄必须在0-120之间',
						trigger: ['change', 'blur'],
					}],
					illnessType: {
						type: 'string',
						required: true,
						message: '请输入病症类型',
						trigger: ['blur']
					},
					rescueDescription: {
						type: 'string',
						required: true,
						message: '请描述施救过程',
						trigger: ['blur']
					},
					online_flag: {
						type: 'string',
						required: true,
						message: '请选择是否连线',
						trigger: ['change']
					},
					remoteGuideRealName: {
						type: 'string',
						required: false, // 动态验证，不在这里设置required
						message: '请选择连线员',
						trigger: ['blur', 'change']
					},
				},
				rescuerNameRule: {
					type: 'string',
					required: true,
					message: '请输入救援人员姓名',
					trigger: ['blur']
				},
				rescuerPhoneRule: [
					{ required: true, message: '请选择救援人员', trigger: ['blur', 'change'] }
				],
				rescuerIdCardRule: [
					{ required: true, message: '请选择救援人员', trigger: ['blur', 'change'] }
				],
				showDatePicker: false,
				datePickerValue: Number(new Date()),
				genderList: [{
					name: '男',
          value: '0',
					disabled: false
				}, {
					name: '女',
          value: '1',
					disabled: false
				}],
				onlineOptions: [{
					name: '否',
					value: '0',
					disabled: false
				}, {
					name: '是',
					value: '1',
					disabled: false
				}],
				areas: areas,
				showUserPicker: false,
				showRemoteGuidePicker: false,
				currentRescuerIndex: null,
				showLocal: false,
				addressColumns: [],
				defaultAddress: [0, 0, 0],
				addressData: areas || [],
				saveLoading: false,
				saveAndSubmitLoading: false,
				// 附件相关数据
				attachmentList: [], // 附件显示列表
				maxAttachmentCount: 5, // 总共最多5个
				maxVideoCount: 2, // 视频最多2个
				defaultVideoCover: '', // 使用黑色背景，不需要图片
				showVideoPreview: false,
				previewVideoUrl: '',
				// 病症类型相关
				showIllnessTypePicker: false,
				illnessTypeOptions: [],
				illnessTypeText: '',
			};
		},
		computed: {
			// 表单是否禁用
			isFormDisabled() {
				// 当正在加载或状态为审核中(2)、已通过(3)时禁用表单
				// 只有状态为草稿(1)和驳回(4)时才允许编辑
				const disabledStatuses = ['2', '3']; // 2:审核中, 3:已通过
				return this.saveLoading || this.saveAndSubmitLoading || this.uploadingCount > 0
					|| disabledStatuses.includes(this.formModel.rescueStatus);
			},
			// 获取申请状态文本
			rescueStatusText() {
				const statusMap = {
					'1': '草稿',
					'2': '审核中',
					'3': '已通过',
					'4': '已驳回'
				};
				return statusMap[this.formModel.rescueStatus] || '未知状态';
			},
			// 是否显示操作按钮
			showActionButtons() {
				// 只有草稿(1)和驳回(4)状态才显示操作按钮
				return ['1', '4'].includes(this.formModel.rescueStatus);
			},
			canAddImage() {
				// 剩余数量>0 且视频未超限
				return this.attachmentList.length < this.maxAttachmentCount && this.videoCount < this.maxVideoCount + this.imageCount;
			},
			canAddVideo() {
				return this.attachmentList.length < this.maxAttachmentCount && this.videoCount < this.maxVideoCount;
			},
			imageCount() {
				return this.attachmentList.filter(item => item.fileType === 'image').length;
			},
			videoCount() {
				return this.attachmentList.filter(item => item.fileType === 'video').length;
			}
		},
		onLoad(options) {
			// 先初始化地址数据
			this.initAddressData();

			// 先加载字典数据，完成后再加载案例数据
			this.loadIllnessTypeDict().then(() => {
				if (options.id) {
					this.caseId = options.id;
					this.pageTitle = '编辑案例详情';
					this.loadCaseData(options.id);
				}
			});
		},
		methods: {
			// 生成附件的唯一key，兼容非H5平台
			getAttachmentKey(item, index) {
				return item.fileId || item.fileUrl || `attachment_${index}`;
			},
			async loadCaseData(id) {
				try {
					uni.showLoading({
						title: '正在加载...'
					});

					const res = await getRescueCaseInfo(id);
					if (res.data) {
						// 处理地址显示文本
						if (res.data.city && !res.data.cityText) {
							// 如果city是代码格式，转换为文本；如果已经是文本，直接使用
							if (/^\d+$/.test(res.data.city)) {
								// city是数字代码，需要转换
								res.data.cityText = this.getCityTextByCode(res.data.city);
							} else {
								// city已经是文本，直接使用
								res.data.cityText = res.data.city;
							}
						}

						// 确保rescuers数组存在
						if (!res.data.rescuers || res.data.rescuers.length === 0) {
							res.data.rescuers = [{
								rescuerName: '',
								rescuerPhone: '',
								rescuerIdCard: '',
								rescuerId: null
							}];
						}

						// 处理附件数据
						if (res.data.attachments && res.data.attachments.length > 0) {
							this.attachmentList = res.data.attachments.map(item => ({
								fileUrl: item.fileUrl, // 统一使用fileUrl
								fileId: item.fileId, // 统一使用 fileId 字段
								fileType: item.fileType, // 添加fileType字段
							}));
							// 同步到 formModel.attachments
							res.data.attachments = [...this.attachmentList];
						} else {
							res.data.attachments = [];
							this.attachmentList = [];
						}
						this.formModel = { ...this.formModel, ...res.data };

						// 设置病症类型显示文本
						this.updateIllnessTypeText();
					}
				} catch (error) {
					console.error('加载案例数据失败:', error);
					uni.showToast({
						title: '加载数据失败',
						icon: 'none'
					});
				} finally {
					uni.hideLoading();
				}
			},
			getCityTextByCode(code) {
				// 根据城市代码获取城市文本
				try {
					for (let province of this.addressData) {
						if (province.areas) {
							for (let city of province.areas) {
								if (city.areas) {
									for (let district of city.areas) {
										if (district.code === code) {
											return `${province.name} ${city.name} ${district.name}`;
										}
									}
								}
							}
						}
					}
				} catch (error) {
					console.error('获取城市文本失败:', error);
				}
				return '';
			},
			goBack() {
				uni.navigateBack();
			},
			onDateConfirm({value}) {
				const date = new Date(value);
				const year = date.getFullYear();
				const month = uni.$u.padZero(date.getMonth() + 1);
				const day = uni.$u.padZero(date.getDate());
				this.formModel.rescueDate = `${year}-${month}-${day}`;
				this.showDatePicker = false;
			},
			initAddressData() {
				try {
					if (!this.addressData || this.addressData.length === 0) {
						uni.showToast({ title: '地址数据加载失败', icon: 'none' });
						return;
					}
					const provinceData = this.addressData.map(province => ({ name: province.name, code: province.code }));
					const firstProvince = this.addressData[0];
					let cityData = [];
					if (firstProvince && firstProvince.areas) {
						cityData = firstProvince.areas.map(city => ({ name: city.name, code: city.code }));
					}
					let districtData = [];
					if (firstProvince && firstProvince.areas && firstProvince.areas[0] && firstProvince.areas[0].areas) {
						districtData = firstProvince.areas[0].areas.map(district => ({ name: district.name, code: district.code }));
					}
					this.addressColumns = [provinceData, cityData, districtData];
				} catch (error) {
					console.error('初始化地址数据失败:', error);
					uni.showToast({ title: '地址数据初始化失败', icon: 'none' });
				}
			},
			showAddressPicker() {
				this.showLocal = true;
			},
			onAddressChange(e) {
				const columnIndex = e.columnIndex;
				const index = e.index;
				const indexs = e.indexs;
				const picker = e.picker || this.$refs.uPicker;
				try {
					if (columnIndex === 0) {
						const selectedProvince = this.addressData[index];
						if (!selectedProvince || !selectedProvince.areas) return;
						const cityData = selectedProvince.areas.map(city => ({ name: city.name, code: city.code }));
						picker.setColumnValues(1, cityData);
						let districtData = [];
						if (selectedProvince.areas[0] && selectedProvince.areas[0].areas) {
							districtData = selectedProvince.areas[0].areas.map(district => ({ name: district.name, code: district.code }));
						}
						picker.setColumnValues(2, districtData);
					}
					if (columnIndex === 1) {
						const selectedProvince = this.addressData[indexs[0]];
						const selectedCity = selectedProvince && selectedProvince.areas && selectedProvince.areas[index];
						if (!selectedCity || !selectedCity.areas) return;
						const districtData = selectedCity.areas.map(district => ({ name: district.name, code: district.code }));
						picker.setColumnValues(2, districtData);
					}
				} catch (error) {
					console.error('地址选择器变化处理失败:', error);
				}
			},
			onAddressConfirm(e) {
				try {
					const values = e.value;
					const province = values[0];
					const city = values[1];
					const district = values[2];
					const cityText = `${province.name} ${city.name} ${district.name}`;
					this.formModel.city = cityText; // 直接保存文本
					this.formModel.cityText = cityText;
					this.showLocal = false;
				} catch (error) {
					console.error('地址确认失败:', error);
					uni.showToast({ title: '地址选择失败', icon: 'none' });
				}
			},
			addRescuer() {
				this.formModel.rescuers.push({
					rescuerName: '',
					rescuerPhone: '',
					rescuerIdCard: '',
					rescuerId: null
				});
			},
			removeRescuer(index) {
				if (this.formModel.rescuers.length <= 1) {
					uni.showToast({
						title: '至少要有一名救援人员',
						icon: 'none'
					});
					return;
				}
				this.formModel.rescuers.splice(index, 1);
			},
			async handleSave(status) {
				if (this.saveLoading || this.saveAndSubmitLoading) return;

				// 根据状态设置不同的loading状态
				if (status === '1') {
					this.saveLoading = true;
				} else {
					this.saveAndSubmitLoading = true;
				}

				try {
					// 先进行基础表单校验
					await this.$refs.uForm.validate();

					// 额外校验救援人员信息
					const rescuerValidationError = this.validateRescuers();
					if (rescuerValidationError) {
            uni.showToast({ title: rescuerValidationError, icon: 'none' });
            return
					}

					// 额外校验连线员信息
					const remoteGuideValidationError = this.validateRemoteGuide();
					if (remoteGuideValidationError) {
            uni.showToast({ title: remoteGuideValidationError, icon: 'none' });
            return
					}

					// 准备提交数据
					const submitData = this.prepareSubmitData();
					submitData.rescueStatus = status;

					let res;
					if (this.caseId) {
						// 更新现有案例
						res = await updateRescueCase(submitData);
					} else {
						// 创建新案例
						res = await saveRescueCase(submitData);
						// 如果是首次保存，保存返回的ID
						if (res.data) {
							this.caseId = res.data;
							this.formModel.id = res.data;
						}
					}

					if (res.code === 200) {
						const message = status === '1' ? '保存成功' : '保存并提交成功';
						uni.showToast({ title: message, icon: 'success' });

						// 如果是保存并提交，成功后返回上一页
						if (status === '2') {
							setTimeout(() => {
								uni.navigateBack();
							}, 1500);
						}
					} else {
						uni.showToast({ title: res.msg || '操作失败', icon: 'none' });
					}
				} catch (error) {
					console.error('操作失败:', error);
					if (error.message && error.message.includes('validate')) {
						uni.showToast({ title: '请填写完整的必填项', icon: 'none' });
					} else {
						const message = status === '1' ? '保存失败' : '保存并提交失败';
						uni.showToast({ title: message, icon: 'none' });
					}
				} finally {
					this.saveLoading = false;
					this.saveAndSubmitLoading = false;
				}
			},
			async onSave() {
				await this.handleSave('1');
			},
			async onSaveAndSubmit() {
				await this.handleSave('2');
			},
			prepareSubmitData() {
				// 准备提交给后端的数据
				const submitData = {
					...this.formModel
				};

				// 如果有ID，包含在提交数据中
				if (this.caseId) {
					submitData.id = this.caseId;
				}

				// 处理救援人员数据，移除空的救援人员
				submitData.rescuers = this.formModel.rescuers.filter(rescuer =>
					rescuer.rescuerName && rescuer.rescuerPhone
				);

				// 处理性别字段，确保是正确的格式
				if (submitData.patientGender === '男') {
					submitData.patientGender = '0';
				} else if (submitData.patientGender === '女') {
					submitData.patientGender = '1';
				}

				// 处理附件数据
				if (this.formModel.attachments && this.formModel.attachments.length > 0) {
					submitData.attachments = this.formModel.attachments.filter(item => item.fileId);
				} else {
					submitData.attachments = [];
				}

				// city字段现在直接保存文本，不需要删除cityText
				// cityText保留用于显示一致性

				return submitData;
			},
			openUserPicker(index) {
				this.currentRescuerIndex = index;
				this.showUserPicker = true;
			},
			chooseUser(user) {
				// 检查用户ID是否有效
				if (!user.userId) {
					uni.showToast({ title: '用户信息无效', icon: 'none' });
					return;
				}

				// 检查是否已经是连线员
				if (this.formModel.remoteGuideUserId === user.userId) {
					uni.showToast({ title: '该用户已是连线员，不能重复添加为救援人员', icon: 'none' });
					return;
				}

				// 检查是否已经在救援人员列表中的其他位置
				const existingIndex = this.formModel.rescuers.findIndex((rescuer, index) =>
					rescuer.rescuerId === user.userId && index !== this.currentRescuerIndex
				);

				if (existingIndex !== -1) {
					uni.showToast({ title: `该用户已是救援人员${existingIndex + 1}，不能重复添加`, icon: 'none' });
					return;
				}

				// 设置救援人员信息
				this.$set(this.formModel.rescuers, this.currentRescuerIndex, {
					rescuerName: user.realName,
					rescuerPhone: user.phoneNumber,
					rescuerIdCard: user.idCard,
					rescuerId: user.userId
				});
				this.showUserPicker = false;
			},
			// 是否连线选项变化处理
			onOnlineChange(value) {
				// 当选择"否"时，清空远程指导人信息
				if (value === '0') {
					this.formModel.remoteGuideUserId = '';
					this.formModel.remoteGuideRealName = '';
				}
			},
			// 打开连线员选择弹窗
			openRemoteGuidePicker() {
				this.showRemoteGuidePicker = true;
			},
			// 选择连线员
			chooseRemoteGuide(user) {
				// 检查用户ID是否有效
				if (!user.userId) {
					uni.showToast({ title: '用户信息无效', icon: 'none' });
					return;
				}

				// 检查是否已经在救援人员列表中
				const existingRescuerIndex = this.formModel.rescuers.findIndex(rescuer =>
					rescuer.rescuerId === user.userId
				);

				if (existingRescuerIndex !== -1) {
					uni.showToast({ title: `该用户已是救援人员${existingRescuerIndex + 1}，不能重复添加为连线员`, icon: 'none' });
					return;
				}

				// 设置连线员信息
				this.formModel.remoteGuideUserId = user.userId;
				this.formModel.remoteGuideRealName = user.realName;
				this.showRemoteGuidePicker = false;
				// 触发表单验证
				this.$refs.uForm.validateField('remoteGuideRealName');
			},
			chooseImage() {
				if (!this.canAddImage) {
					this.$u.toast(`最多只能上传${this.maxAttachmentCount}个附件，且视频最多${this.maxVideoCount}个`);
					return;
				}
				uni.chooseImage({
					count: this.maxAttachmentCount - this.attachmentList.length,
					success: (res) => {
						const filePath = res.tempFilePaths[0];
						uni.getImageInfo({
							src: filePath,
							success: (info) => {
								const isJpgOrPng = info.type === 'jpg' || info.type === 'jpeg' || info.type === 'png';
								if (!isJpgOrPng) {
									this.$u.toast('仅支持JPG/PNG格式图片');
									return;
								}
								uni.getFileSystemManager().getFileInfo({
									filePath,
									success: (fileInfo) => {
										if (fileInfo.size > 5 * 1024 * 1024) {
											this.$u.toast('图片大小不能超过5MB');
											return;
										}
										uni.showLoading({title: '上传中...'});
										uploadImage(filePath, (fileUrl, fileId) => {
											uni.hideLoading();
											const attachment = { fileUrl, fileId, fileType: 'image' };
											this.attachmentList.push(attachment);
											// 同步更新 formModel.attachments
											this.formModel.attachments.push(attachment);
										}, err => {
											uni.hideLoading();
											this.$u.toast('图片上传失败');
										});
									},
									fail: () => {
										this.$u.toast('图片读取失败');
									}
								});
							},
							fail: () => {
								this.$u.toast('图片信息获取失败');
							}
						});
					}
				});
			},
			chooseVideo() {
				if (!this.canAddVideo) {
					this.$u.toast(`最多只能上传${this.maxVideoCount}个视频`);
					return;
				}
				uni.chooseVideo({
					maxDuration: 60,
					compressed: true,
					success: (res) => {
						const filePath = res.tempFilePath;
						if (!filePath) return;
						// 校验格式和大小
						uni.getFileSystemManager().getFileInfo({
							filePath,
							success: (fileInfo) => {
								if (fileInfo.size > 50 * 1024 * 1024) {
									this.$u.toast('视频大小不能超过50MB');
									return;
								}
								uni.showLoading({title: '上传中...'});
								uploadImage(filePath, (fileUrl, fileId) => {
									uni.hideLoading();
									// 获取视频封面
									uni.createVideoContext && uni.createVideoContext('video_' + fileId);
									const attachment = { fileUrl, fileId, type: 'video', fileType: 'video' };
									this.attachmentList.push(attachment);
									// 同步更新 formModel.attachments
									this.formModel.attachments.push(attachment);
								}, err => {
									uni.hideLoading();
									this.$u.toast('视频上传失败');
								});
							},
							fail: () => {
								this.$u.toast('视频读取失败');
							}
						});
					}
				});
			},
			deleteAttachment(idx) {
				this.attachmentList.splice(idx, 1);
				// 同步删除 formModel.attachments 中对应的项
				this.formModel.attachments.splice(idx, 1);
			},
			previewAttachment(idx) {
				const item = this.attachmentList[idx];
				if (item.fileType === 'image') {
					uni.previewImage({
						urls: this.attachmentList.filter(i => i.fileType === 'image').map(i => i.fileUrl),
						current: item.fileUrl
					});
				} else if (item.fileType === 'video') {
					this.previewVideoUrl = item.fileUrl;
					this.showVideoPreview = true;
				}
			},
			// 加载病症类型字典
			async loadIllnessTypeDict() {
				try {
					const res = await getDicts('rescue_disease_type');
					this.illnessTypeOptions = res.data.map(item => ({
						label: item.dictLabel,
						value: item.dictValue
					}));
				} catch (error) {
					console.error('获取病症类型字典失败:', error);
					uni.showToast({
						title: '获取病症类型失败',
						icon: 'none'
					});
				}
			},
			// 病症类型选择确认
			onIllnessTypeConfirm(e) {
				this.illnessTypeText = e.value[0].label;
				this.formModel.illnessType = e.value[0].value;
				this.showIllnessTypePicker = false;
				this.$refs.uForm.validateField('illnessType');
			},
			// 更新病症类型显示文本
			updateIllnessTypeText() {
				if (this.formModel.illnessType && this.illnessTypeOptions.length > 0) {
					const option = this.illnessTypeOptions.find(item => item.value === this.formModel.illnessType);
					this.illnessTypeText = option ? option.label : this.formModel.illnessType;
				}
			},

			// 校验所有救援人员信息
			validateRescuers() {
				for (let i = 0; i < this.formModel.rescuers.length; i++) {
					const rescuer = this.formModel.rescuers[i];

					// 校验是否选择了救援人员
					if (!rescuer.rescuerName || rescuer.rescuerName.trim() === '') {
						return `请选择救援人员${i + 1}`;
					}

					// 校验是否有完整的人员信息（选择人员后应该自动填充）
					if (!rescuer.rescuerPhone || !rescuer.rescuerIdCard) {
						return `救援人员${i + 1}信息不完整，请重新选择`;
					}
				}
				return null; // 校验通过
			},

			// 校验连线员信息
			validateRemoteGuide() {
				// 如果选择了连线，则必须选择连线员
				if (this.formModel.online_flag === '1') {
					if (!this.formModel.remoteGuideRealName || this.formModel.remoteGuideRealName.trim() === '') {
						return '请选择连线员';
					}
					if (!this.formModel.remoteGuideUserId) {
						return '连线员信息不完整，请重新选择';
					}
				}
				return null; // 校验通过
			},
		},
		onReady() {
			//onReady 为uni-app支持的生命周期之一
			this.$refs.uForm.setRules(this.rules)
		},
	};
</script>

<style lang="scss" scoped>
	.container {
		background-color: #f2f2f2;
	}

  .form-wrapper {
		margin: 12rpx;
		padding-bottom: 120rpx;
	}

	.form-section {
		background-color: #fff;
		margin-bottom: 20rpx;
		border-radius: 12rpx;
		//overflow: hidden;

		.section-title {
			font-size: 32rpx;
			font-weight: bold;
			padding: 20rpx 30rpx;
			border-bottom: 1px solid #f5f5f5;
		}
	}

	.rescuer-list {
		padding: 0 20rpx;
	}

	.submit-button-wrapper {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		padding: 20rpx 24rpx 32rpx 24rpx;
		background-color: #fff;
		box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.05);
		z-index: 100;
	}

	.input-with-btn {
		display: flex;
		align-items: center;
		position: relative;
		.input-btn {
			position: static;
			margin-left: 8rpx;
			top: auto;
			right: auto;
			transform: none;
			z-index: 2;
		}
	}

	.rescuer-select-input {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx 0;
		cursor: pointer;

		.rescuer-name-text {
			font-size: 28rpx;
			color: #303133;
		}

		.placeholder-text {
			font-size: 28rpx;
			color: #c0c4cc;
		}

		.input-btn {
			margin-left: 8rpx;
		}
	}

	.rescuer-select-input.disabled {
		opacity: 0.6;
		pointer-events: none;
	}

	.rescuer-card {
		border: 1px solid #e5e5e5;
		border-radius: 10rpx;
		padding: 20rpx 16rpx 8rpx 16rpx;
		margin-bottom: 18rpx;
		background: #fff;
		position: relative;
	}

	.delete-btn {
		position: absolute;
		top: 12rpx;
		right: 12rpx;
		width: 36rpx;
		height: 36rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #fff;
		border-radius: 50%;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
		z-index: 10;
		cursor: pointer;
	}

	.delete-btn:hover {
		background-color: #f5f5f5;
	}

	/* 状态提示样式 */
	.status-tip {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 32rpx;
		margin-top: 48rpx;
		border-radius: 8rpx;
	}

	.status-tip.reviewing {
		background: #fff7e6;
		border: 2rpx solid #ffd591;
	}

	.status-tip.approved {
		background: #f6ffed;
		border: 2rpx solid #b7eb8f;
	}

	.status-tip.rejected {
		background: #fff2f0;
		border: 2rpx solid #ffccc7;
	}

	.status-text {
		margin-left: 16rpx;
		font-size: 28rpx;
		font-weight: 500;
	}

	.status-tip.reviewing .status-text {
		color: #ff9500;
	}

	.status-tip.approved .status-text {
		color: #19be6b;
	}

	.status-tip.rejected .status-text {
		color: #fa3534;
	}

	.address-input {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx 0;
		.address-text {
			font-size: 28rpx;
			color: #303133;
		}
		.placeholder-text {
			font-size: 28rpx;
			color: #c0c4cc;
		}
	}

	.address-input.disabled {
		opacity: 0.6;
		pointer-events: none;
	}

	.textarea-bordered {
		border: 1px solid #e5e5e5;
		border-radius: 10rpx;
		padding: 16rpx;
		background: #fff;
	}

	.rescuer-tips {
		color: #faad14;
		font-size: 24rpx;
		margin: 0 0 10rpx 16rpx;
	}

	.user-search-popup {
		position: relative;
		width: 320px;
		background: #fff;
		border-radius: 16rpx;
		box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.10);
		padding: 32rpx 24rpx 24rpx 24rpx;
		box-sizing: border-box;
	}
	.popup-close {
		position: absolute;
		right: 18rpx;
		top: 18rpx;
		color: #bbb;
		z-index: 2;
	}
	.popup-title {
		font-size: 30rpx;
		font-weight: bold;
		text-align: center;
		margin-bottom: 24rpx;
	}
	.popup-input-row {
		display: flex;
		align-items: center;
		margin-bottom: 18rpx;
	}
	.popup-input {
		flex: 1;
		margin-right: 12rpx;
		margin-bottom: 0;
	}
	.popup-search-btn {
		flex-shrink: 0;
		width: 80rpx;
		margin-bottom: 0;
	}
	.popup-result-list {
		max-height: 260rpx;
		overflow-y: auto;
		margin-bottom: 8rpx;
	}
	.popup-result-item {
		padding: 14rpx 0;
		border-bottom: 1px solid #f0f0f0;
		color: #333;
		font-size: 28rpx;
		cursor: pointer;
	}
	.popup-result-item:last-child {
		border-bottom: none;
	}
	.popup-no-result {
		color: #bbb;
		text-align: center;
		font-size: 26rpx;
		margin-top: 20rpx;
	}
	.multi-photo-upload {
		display: flex;
		flex-wrap: wrap;
		gap: 20rpx;
		min-height: 100rpx;
	}
	.photo-thumb {
		position: relative;
		width: 160rpx;
		height: 160rpx;
		border-radius: 8rpx;
		overflow: hidden;
		background: #f5f7fa;
	}
	.thumb-image {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}
	.add-photo-btn {
		width: 160rpx;
		height: 160rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		border: 2rpx dashed #dcdfe6;
		border-radius: 8rpx;
		background: #f5f7fa;
		cursor: pointer;
	}
	.add-photo-btn .upload-text {
		font-size: 24rpx;
		color: #909399;
		margin-top: 10rpx;
	}
	.photo-thumb .delete-btn {
		position: absolute;
		top: 0;
		right: 0;
		width: 40rpx;
		height: 40rpx;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		border-bottom-left-radius: 8rpx;
		z-index: 2;
	}
	.form-tips {
		font-size: 24rpx;
		color: #909399;
		margin-top: 10rpx;
	}
	.video-thumb {
		position: relative;
		width: 160rpx;
		height: 160rpx;
		border-radius: 8rpx;
		overflow: hidden;
		background: #000;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	.video-icon {
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
		z-index: 2;
	}
	.video-preview-modal {
		position: fixed;
		left: 0;
		top: 0;
		width: 100vw;
		height: 100vh;
		z-index: 9999;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	.video-preview-mask {
		position: absolute;
		left: 0;
		top: 0;
		width: 100vw;
		height: 100vh;
		background: rgba(0,0,0,0.7);
		z-index: 1;
	}
	.video-preview-player {
		width: 80vw;
		max-width: 600px;
		height: 45vw;
		max-height: 340px;
		z-index: 2;
		border-radius: 12rpx;
		background: #000;
	}
	.video-preview-close {
		position: absolute;
		top: 40rpx;
		right: 40rpx;
		z-index: 3;
	}

	/* 修复必填星号显示问题 - 解决overflow: hidden导致的裁剪 */
	/deep/ .u-form-item__body__left__content__required{
		position: relative !important;
		left: 0 !important;
		color: #f56c6c !important;
		font-size: 28rpx !important;
	}

	/* 确保表单项容器不会裁剪必填星号 */
	/deep/ .u-form-item {
		overflow: visible !important;
	}

	/* 确保表单项标签容器不会裁剪必填星号 */
	/deep/ .u-form-item__label {
		overflow: visible !important;
		position: relative !important;
	}
</style>
