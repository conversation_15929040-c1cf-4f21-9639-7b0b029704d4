<template>
  <view>
    <Navbar :hideBtn="false" bgColor="#f3f4f6" title="新增收货地址"></Navbar>
    <view style="padding: 40rpx;">
      <!-- 地址表单 -->
      <u--form
          labelPosition="left"
          :model="form"
          :rules="rules"
          ref="uForm"
          labelWidth="160rpx"
      >
        <!-- 选择地址 -->
        <u-form-item label="选择省市区" prop="address" borderBottom required>
          <view @click="showAddressPicker" class="address-input">
            <text v-if="form.address" class="address-text">{{ form.address }}</text>
            <text v-else class="placeholder-text">请选择省市区</text>
            <u-icon name="arrow-right" color="#c0c4cc" size="14"></u-icon>
          </view>

          <u-picker
              :show="showLocal"
              :columns="addressColumns"
              @confirm="onAddressConfirm"
              @cancel="showLocal = false"
              @close="showLocal = false"
              @change="onAddressChange"
              title="请选择所在地"
              keyName="name"
              itemHeight="80"
              closeOnClickOverlay
              ref="uPicker"
              :defaultIndex="defaultAddress"
              immediateChange
          ></u-picker>
        </u-form-item>

        <!-- 详细地址 -->
        <u-form-item label="详细地址" prop="detail" borderBottom required>
          <u--input
              v-model="form.detail"
              placeholder="请填写详细地址，如：XX路XX号XX室"
              border="none"
              clearable
              maxlength="100"
              @blur="trimInput('detail')"
          />
        </u-form-item>

        <!-- 联系人姓名 -->
        <u-form-item label="联系人" prop="name" borderBottom required>
          <u--input
              v-model="form.name"
              placeholder="请填写收货人姓名"
              border="none"
              clearable
              maxlength="20"
              @blur="trimInput('name')"
          />
        </u-form-item>

        <!-- 手机号 -->
        <u-form-item label="手机号" prop="phone" borderBottom required>
          <u--input
              v-model="form.phone"
              placeholder="请填写收货人手机号"
              border="none"
              clearable
              type="number"
              maxlength="11"
          />
        </u-form-item>

        <!-- 性别选择 -->
        <u-form-item label="性别" prop="sex" borderBottom required>
          <view @click="showGenderPicker = true" class="gender-input">
            <text v-if="sexText" class="gender-text">{{ sexText }}</text>
            <text v-else class="placeholder-text">请选择性别</text>
            <u-icon name="arrow-right" color="#c0c4cc" size="14"></u-icon>
          </view>
        </u-form-item>

        <!-- 默认地址开关 -->
        <u-form-item label="设为默认" prop="defaultFlag" borderBottom>
          <u-switch v-model="form.defaultFlag" activeValue="Y" inactiveValue="N" @change="onSwitchChange"></u-switch>
        </u-form-item>
      </u--form>

      <!-- 提示信息 -->
      <view class="tips-container">
        <view class="tips">
          <text>💡 请确保收货地址信息填写准确，以免影响物件配送</text>
        </view>
      </view>
    </view>

    <!-- 性别选择器 -->
    <u-action-sheet
        :show="showGenderPicker"
        :actions="genderOptions"
        title="请选择性别"
        @close="showGenderPicker = false"
        @select="onGenderSelect"
    />

    <!-- 底部按钮 -->
    <view class="bottom-buttons">
      <!-- 保存按钮 -->
      <u-button text="保存地址" size="normal" type="primary" @click="onSave" :loading="saveLoading"
                :disabled="saveLoading"/>
    </view>
  </view>
</template>

<script>
import Navbar from '@/components/navbar/Navbar'
import areas from "@/utils/areas.json"
import {addAddress, getAddressInfo, updateAddress} from "@/api/center/user_address/user_address";

export default {
  name: 'AddressForm',
  components: {
    Navbar
  },
  data() {
    return {
      // 表单数据
      form: {
        id: '', // 地址ID，用于编辑
        name: '',
        phone: '',
        sex: '',
        province: '',
        city: '',
        district: '',
        address: '', // 显示用的完整地址
        detail: '',
        defaultFlag: 'N'
      },

      // UI状态
      sexText: '',
      showGenderPicker: false,
      showLocal: false,
      saveLoading: false,
      submitLoading: false,

      // 选项数据
      genderOptions: [
        {name: '先生', value: '0'},
        {name: '女士', value: '1'}
      ],

      // 地址选择器相关
      addressColumns: [],
      defaultAddress: [0, 0, 0],
      addressData: areas || [],

      // 表单验证规则
      rules: {
        name: [
          {required: true, message: '请输入联系人姓名', trigger: ['blur']},
          {min: 2, max: 20, message: '姓名长度应在2-20个字符之间', trigger: ['blur']}
        ],
        phone: [
          {required: true, message: '请输入手机号', trigger: ['blur']},
          {
            // 自定义验证函数，见上说明
            validator: (rule, value, callback) => {
              // 上面有说，返回true表示校验通过，返回false表示不通过
              // uni.$u.test.mobile()就是返回true或者false的
              return uni.$u.test.mobile(value);
            },
            message: '手机号码不正确',
            // 触发器可以同时用blur和change
            trigger: ['blur'],
          }
        ],
        sex: [
          {required: true, message: '请选择性别', trigger: ['change']}
        ],
        address: [
          {required: true, message: '请选择收货地址', trigger: ['change']}
        ],
        detail: [
          {required: true, message: '请输入详细地址', trigger: ['blur']},
          {min: 5, max: 100, message: '详细地址长度应在5-100个字符之间', trigger: ['blur']}
        ]
      }
    }
  },

  onLoad: function (options) {
    this.initAddressData()
    this.$refs.uForm.setRules(this.rules)
    // 如果是编辑模式，加载现有地址数据
    if (options && options.id) {
      this.loadAddressData(options.id)
    }
  },

  methods: {
    /**
     * 初始化地址数据
     */
    initAddressData: function () {
      try {
        if (!this.addressData || this.addressData.length === 0) {
          uni.showToast({
            title: '地址数据加载失败',
            icon: 'none'
          })
          return
        }

        // 构建省份数据
        const provinceData = this.addressData.map(function (province) {
          return {
            name: province.name,
            code: province.code
          }
        })

        // 构建默认城市数据（第一个省份的城市）
        const firstProvince = this.addressData[0]
        let cityData = []
        if (firstProvince && firstProvince.areas) {
          cityData = firstProvince.areas.map(function (city) {
            return {
              name: city.name,
              code: city.code
            }
          })
        }

        // 构建默认区县数据（第一个城市的区县）
        let districtData = []
        if (firstProvince && firstProvince.areas && firstProvince.areas[0] && firstProvince.areas[0].areas) {
          districtData = firstProvince.areas[0].areas.map(function (district) {
            return {
              name: district.name,
              code: district.code
            }
          })
        }

        this.addressColumns = [provinceData, cityData, districtData]
      } catch (error) {
        console.error('初始化地址数据失败:', error)
        uni.showToast({
          title: '地址数据初始化失败',
          icon: 'none'
        })
      }
    },

    /**
     * 显示地址选择器
     */
    showAddressPicker: function () {
      this.showLocal = true
    },

    /**
     * 地址选择器变化处理
     */
    onAddressChange: function (e) {
      const columnIndex = e.columnIndex
      const index = e.index
      const indexs = e.indexs
      const picker = e.picker || this.$refs.uPicker

      try {
        // 当省份改变时
        if (columnIndex === 0) {
          const selectedProvince = this.addressData[index]
          if (!selectedProvince || !selectedProvince.areas) return

          // 更新城市列表
          const cityData = selectedProvince.areas.map(function (city) {
            return {
              name: city.name,
              code: city.code
            }
          })
          picker.setColumnValues(1, cityData)

          // 更新区县列表
          let districtData = []
          if (selectedProvince.areas[0] && selectedProvince.areas[0].areas) {
            districtData = selectedProvince.areas[0].areas.map(function (district) {
              return {
                name: district.name,
                code: district.code
              }
            })
          }
          picker.setColumnValues(2, districtData)
        }

        // 当城市改变时
        if (columnIndex === 1) {
          const selectedProvince = this.addressData[indexs[0]]
          const selectedCity = selectedProvince && selectedProvince.areas && selectedProvince.areas[index]
          if (!selectedCity || !selectedCity.areas) return

          // 更新区县列表
          const districtData = selectedCity.areas.map(function (district) {
            return {
              name: district.name,
              code: district.code
            }
          })
          picker.setColumnValues(2, districtData)
        }
      } catch (error) {
        console.error('地址选择器变化处理失败:', error)
      }
    },

    /**
     * 地址选择确认
     */
    onAddressConfirm: function (e) {
      try {
        const values = e.value
        const province = values[0]
        const city = values[1]
        const district = values[2]

        // 更新表单数据
        this.form.province = province.name
        this.form.city = city.name
        this.form.district = district.name
        this.form.address = `${province.name} ${city.name} ${district.name}`

        this.showLocal = false

      } catch (error) {
        console.error('地址确认失败:', error)
        uni.showToast({
          title: '地址选择失败',
          icon: 'none'
        })
      }
    },

    /**
     * 性别选择
     */
    onGenderSelect: function (item) {
      this.form.sex = item.value
      this.sexText = item.name
      this.showGenderPicker = false

      // 手动触发表单验证
      this.$refs.uForm.validateField('sex')
    },

    onSwitchChange(value) {
      // 使用 nextTick 确保DOM更新完成
      this.$nextTick(() => {
        this.form.defaultFlag = value;
        // 避免触发全局样式重新计算
      });
    },
    /**
     * 去除输入内容首尾空格
     */
    trimInput(field) {
      if (this.form[field]) {
        this.form[field] = this.form[field].trim()
      }
    },

    /**
     * 表单验证
     */
    validateForm() {
      return new Promise((resolve) => {
        this.$refs.uForm.validate().then(() => {
          resolve(true)
        }).catch(function (error) {
          console.error('表单验证失败:', error)
          resolve(false)
        })
      })
    },

    /**
     * 保存地址
     */
    onSave() {
      if (this.saveLoading) return
      this.form.address = `${this.form.province} ${this.form.city} ${this.form.district} ${this.form.detail}`
      // 手动触发表单验证
      this.$refs.uForm.validateField('address')
      this.validateForm().then((isValid) => {
        if (!isValid) {
          uni.showToast({
            title: '请完善表单信息',
            icon: 'none'
          })
          return
        }

        this.saveLoading = true
        if (!this.form.id) {
          // 调用保存方法
          addAddress(this.form).then((result) => {
            uni.showToast({
              title: result.msg || '保存成功',
              icon: 'success'
            })
            uni.navigateBack()
          }).catch((error) => {
            console.error('保存地址失败:', error)
            uni.showToast({
              title: error.msg || '保存失败，请重试',
              icon: 'none'
            })
          }).finally(() => {
            this.saveLoading = false
          })
        } else {
          // 调用更新方法
          updateAddress(this.form).then((result) => {
            uni.showToast({
              title: result.msg || '更新成功',
              icon: 'success'
            })
          }).catch((error) => {
            console.error('更新地址失败:', error)
            uni.showToast({
              title: error.msg || '更新失败，请重试',
              icon: 'none'
            })
          }).finally(() => {
            this.saveLoading = false
          })
        }
      })
    },

    /**
     * 加载现有地址数据（编辑模式）
     */
    loadAddressData(addressId) {
      getAddressInfo(addressId).then((result) => {
        if (result.data) {
          this.fillFormData(result.data)
        } else {
          uni.showToast({
            title: result.message || '加载地址失败',
            icon: 'none'
          })
        }
      }).catch(function (error) {
        console.error('加载地址数据失败:', error)
        uni.showToast({
          title: '加载地址数据失败',
          icon: 'none'
        })
      })
    },

    /**
     * 填充表单数据
     */
    fillFormData(data) {
      // 填充基本信息
      this.form.id = data.id || ''
      this.form.name = data.name || ''
      this.form.phone = data.phone || ''
      this.form.sex = data.sex || ''
      this.form.province = data.province || ''
      this.form.city = data.city || ''
      this.form.district = data.district || ''
      this.form.detail = data.detail || ''
      this.form.defaultFlag = data.defaultFlag || 'N'

      // 设置显示文本
      if (data.province && data.city && data.district) {
        this.form.address = `${data.province} ${data.city} ${data.district}`
      }

      if (data.sex === '0') {
        this.sexText = '先生'
      } else if (data.sex === '1') {
        this.sexText = '女士'
      }

      // 设置地址选择器的默认值
      if (data.province && data.city && data.district) {
        this.setDefaultAddress(data.province, data.city, data.district)
      }
    },

    /**
     * 设置默认地址选中项
     */
    setDefaultAddress(provinceName, cityName, districtName) {
      try {
        let provinceIndex = 0
        let cityIndex = 0
        let districtIndex = 0

        // 查找省份索引
        for (let i = 0; i < this.addressData.length; i++) {
          if (this.addressData[i].name === provinceName) {
            provinceIndex = i

            // 查找城市索引
            const provinceData = this.addressData[i]
            if (provinceData.areas) {
              for (let j = 0; j < provinceData.areas.length; j++) {
                if (provinceData.areas[j].name === cityName) {
                  cityIndex = j

                  // 查找区县索引
                  const cityData = provinceData.areas[j]
                  if (cityData.areas) {
                    for (let k = 0; k < cityData.areas.length; k++) {
                      if (cityData.areas[k].name === districtName) {
                        districtIndex = k
                        break
                      }
                    }
                  }
                  break
                }
              }
            }
            break
          }
        }

        this.defaultAddress = [provinceIndex, cityIndex, districtIndex]

        // 重新初始化地址选择器数据
        this.initAddressColumnsForEdit(provinceIndex, cityIndex)

      } catch (error) {
        console.error('设置默认地址失败:', error)
      }
    },

    /**
     * 为编辑模式初始化地址选择器数据
     */
    initAddressColumnsForEdit(provinceIndex, cityIndex) {
      try {
        const provinceData = this.addressData.map(function (province) {
          return {
            name: province.name,
            code: province.code
          }
        })

        let cityData = []
        if (this.addressData[provinceIndex] && this.addressData[provinceIndex].areas) {
          cityData = this.addressData[provinceIndex].areas.map(function (city) {
            return {
              name: city.name,
              code: city.code
            }
          })
        }

        let districtData = []
        if (this.addressData[provinceIndex] &&
            this.addressData[provinceIndex].areas &&
            this.addressData[provinceIndex].areas[cityIndex] &&
            this.addressData[provinceIndex].areas[cityIndex].areas) {
          districtData = this.addressData[provinceIndex].areas[cityIndex].areas.map(function (district) {
            return {
              name: district.name,
              code: district.code
            }
          })
        }
        this.addressColumns = [provinceData, cityData, districtData]
      } catch (error) {
        console.error('初始化编辑模式地址数据失败:', error)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.address-input, .gender-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;

  .address-text, .gender-text {
    font-size: 28rpx;
    color: #303133;
  }

  .placeholder-text {
    font-size: 28rpx;
    color: #c0c4cc;
  }
}

.tips-container {
  padding: 24rpx 0;
}

.tips {
  color: #faad14;
  font-size: 24rpx;
  text-align: center;
  padding: 16rpx;
  background-color: #fffbf0;
  border-radius: 8rpx;
  border: 1rpx solid #ffe58f;
}

.bottom-buttons {
  padding: 20rpx 40rpx 40rpx;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 999;
  display: flex;
  justify-content: center; /* Center button horizontally */
}

// 保存按钮样式
.u-button-save {
  background-color: #007aff; // 设置蓝色背景
  color: #ffffff; // 设置白色字体
  height: 88rpx; // 高度
  font-size: 32rpx; // 字体大小
  font-weight: 500; // 字体粗细
  text-align: center; // 居中对齐
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 8rpx; // 圆角
}
</style>
