<template>
  <view class="page-container">
    <Navbar :hideBtn="false" bgColor="#f3f4f6" title="地址列表" :fixed="false"></Navbar>
    <view class="search-bar">
      <input v-model="searchValue" placeholder="请输入联系人姓名" @confirm="onSearch"/>
      <button @click="onSearch">搜索</button>
    </view>
    <view class="list-container">
      <DataList
          ref="dataList"
          :labels="labels"
          :data="listData"
          :loading="loading"
          :pagination="pagination"
          @refresh="onRefresh"
          @load-more="onLoadMore"
          @item-click="onItemClick"
          @swipe-action="onSwipeAction"
          :rightOptions="rightOptions"
      />
    </view>
    <button class="fab-add" @click="onAddAddress">新增地址</button>
  </view>
</template>

<script>
import Navbar from "@/components/navbar/Navbar";
import DataList from "@/components/data-list/DataList";
import {getAddressList, deleteAddress} from "@/api/center/user_address/user_address"; // 地址列表API

export default {
  components: {Navbar, DataList},
  data() {
    return {
      labels: [
        {label: '姓名', prop: 'name'},
        {label: '联系电话', prop: 'phone'},
        {label: '地址', prop: 'address'},
        {label: '是否默认', prop: 'defaultFlag'}
      ],
      listData: [],
      loading: false,
      pagination: {
        page: 1,
        pageSize: 10,
        total: 0,
      },
      searchValue: '',
      rightOptions: [
        {text: '删除', style: {backgroundColor: '#ff4d4f', color: '#fff'}}
      ]
    };
  },
  onShow() {
    this.getList();
  },
  methods: {
    getList(append = false) {
      this.loading = true;
      getAddressList({
        pageNum: this.pagination.page,
        pageSize: this.pagination.pageSize,
        searchValue: this.searchValue,
      }).then(res => {
        let rows = res.rows;
        rows.forEach(item => {
          item.defaultFlag = item.defaultFlag == 'Y' ? '是' : '否';
        });
        if (append) {
          this.listData = this.listData.concat(rows);
        } else {
          this.listData = rows;
        }
        this.pagination.total = res.total;
      }).finally(() => {
        this.loading = false;
        // 停止 DataList 组件的刷新状态
        if (this.$refs.dataList) {
          this.$refs.dataList.stopRefresh();
        }
      });
    },
    onItemClick(item) {
      uni.navigateTo({
        url: `/pageA/user_address/form?id=${item.id}`,
      });
    },
    onSearch() {
      this.pagination.page = 1;
      this.getList(false);
    },
    onRefresh() {
      this.pagination.page = 1;
      this.getList(false);
    },
    onLoadMore() {
      if (this.pagination.page < Math.ceil(this.pagination.total / this.pagination.pageSize)) {
        this.pagination.page++;
        this.getList(true);
      }
    },
    onSwipeAction({action, item, index}) {
      if (action === '删除') {
        deleteAddress(item.id).then(res => {
          uni.showToast({title: '删除成功', icon: 'success'});
          this.getList()
        })
      }
    },
    onAddAddress() {
      uni.navigateTo({url: '/pageA/user_address/form'});
    },
  },
};
</script>

<style lang="scss" scoped>
.page-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden; // 禁止页面滚动
  background-color: #f5f5f5;
}

.search-bar {
  display: flex;
  align-items: center;
  padding: 16rpx;
  background: #fff;
  border-radius: 8rpx;
  margin: 16rpx;
  gap: 16rpx;
  flex-shrink: 0;
}

.search-bar input {
  flex: 1;
  border: 1rpx solid #eee;
  border-radius: 8rpx;
  padding: 8rpx 16rpx;
}

.search-bar button {
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  padding: 8rpx 24rpx;
}

.list-container {
  flex: 1;
  padding: 0 16rpx;
}

.fab-add {
  position: fixed;
  right: 48rpx;
  bottom: 120rpx;
  z-index: 99;
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 16rpx;
  min-width: 180rpx;
  height: 80rpx;
  font-size: 32rpx;
  box-shadow: 0 4rpx 16rpx #bbb;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 32rpx;
}
</style>
