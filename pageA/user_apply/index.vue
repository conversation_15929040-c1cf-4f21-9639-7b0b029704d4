<template>
  <view class="user-apply-form">
    <Navbar :hideBtn="false" bgColor="#f3f4f6" title="用户身份申请"></Navbar>

    <!-- 重要提示 -->
    <view class="important-tips">
      <u-icon name="info-circle-fill" color="#ff9500" size="32"/>
      <text class="tips-text">重要提示：申请不支持绑定他人信息，一旦申请成功，信息不能再次修改，请确保填写信息准确无误。</text>
    </view>

    <view v-if="form.userType" class="user-type-banner">
      当前申请身份：
      <text class="user-type-text">{{ userTypeLabel }}</text>
      <text v-if="form.applyStatus && form.applyStatus !== '1'" class="status-badge" :class="'status-' + form.applyStatus">
        {{ applyStatusText }}
      </text>
    </view>
    <u-form ref="uForm" :model="form" :rules="rules" label-width="120">
      <!-- 真实姓名 -->
      <u-form-item label="真实姓名" prop="realName" required>
        <u-input v-model="form.realName" placeholder="请输入真实姓名" maxlength="30" :disabled="isFormDisabled"/>
      </u-form-item>

      <!-- 证件类型 -->
      <u-form-item label="证件类型" prop="cardType" required @click="!isFormDisabled && (showCardTypePicker = true)">
        <u-input v-model="cardTypeText" disabled disabledColor="#ffffff" placeholder="请选择证件类型">
          <template #suffix>
            <u-icon name="arrow-down" size="18" :color="isFormDisabled ? '#c8c9cc' : '#909399'"/>
          </template>
        </u-input>
      </u-form-item>

      <!-- 证件号码 -->
      <u-form-item label="证件号码" prop="idCard" required>
        <u-input v-model="form.idCard" placeholder="请输入证件号码" :disabled="isFormDisabled"/>
        <u-button slot="right" size="mini" type="primary" @click="chooseIdCardImage" :custom-style="{height: '40px'}" :disabled="isFormDisabled">识别
        </u-button>
      </u-form-item>

      <!-- 性别 -->
      <u-form-item label="性别" prop="sex">
        <u-radio-group v-model="form.sex" placement="row" :disabled="isFormDisabled">
          <u-radio v-for="item in sexOptions" :key="item.value" :label="item.label" :name="item.value" :disabled="isFormDisabled"/>
        </u-radio-group>
      </u-form-item>

      <!-- 手机号码 -->
      <u-form-item label="手机号码" prop="phoneNumber" required>
        <u-input v-model="form.phoneNumber" placeholder="请输入手机号码" type="number" maxlength="11" :disabled="isFormDisabled"/>
      </u-form-item>

      <!-- 邀请人姓名 -->
      <u-form-item v-if="showInviterField" label="邀请人姓名" prop="inviterName" @click="!isFormDisabled && (showInviterPopup = true)">
        <u-input v-model="form.inviterName" placeholder="选填，邀请人姓名" maxlength="30" readonly :disabled="isFormDisabled"/>
      </u-form-item>

      <!-- 照片上传 -->
      <u-form-item label="付款凭证" prop="photoUrl">
        <view class="photo-upload" :class="{ 'disabled': isFormDisabled }">
          <view v-if="form.photoUrl" class="preview-container">
            <image :src="form.photoUrl" mode="aspectFill" class="preview-image" @click="previewImage('photoUrl')"/>
            <view v-if="!isFormDisabled" class="delete-btn" @click.stop="deletePhoto('photoUrl')">
              <u-icon name="close" color="#ffffff" size="20"/>
            </view>
          </view>
          <view v-else class="upload-placeholder" @click="!isFormDisabled && chooseImage('photoUrl', 'photoId')">
            <u-icon name="camera-fill" size="40" :color="isFormDisabled ? '#c8c9cc' : '#909399'"/>
            <text class="upload-text" :style="{ color: isFormDisabled ? '#c8c9cc' : '#909399' }">点击上传</text>
          </view>
        </view>
        <view class="form-tips">（选填）上传付款凭证可加快审核进度，建议如有付款请及时上传。</view>
      </u-form-item>

      <!-- 提交按钮 -->
      <view class="form-submit-btns" v-if="form.applyStatus === '1' || form.applyStatus === '4'">
        <u-row gutter="32">
          <u-col span="6">
            <u-button text="保存" plain @click="onSave" :loading="saveLoading" :disabled="saveLoading || saveAndSubmitLoading" />
          </u-col>
          <u-col span="6">
            <u-button text="保存并提交" type="primary" @click="onSaveAndSubmit" :loading="saveAndSubmitLoading" :disabled="saveLoading || saveAndSubmitLoading" />
          </u-col>
        </u-row>
      </view>

      <!-- 申请状态提示 -->
      <view v-if="form.applyStatus === '2'" class="status-tip reviewing">
        <u-icon name="clock-fill" color="#ff9500" size="24"/>
        <text class="status-text">申请审核中，请耐心等待</text>
      </view>

      <view v-if="form.applyStatus === '3'" class="status-tip approved">
        <u-icon name="checkmark-circle-fill" color="#19be6b" size="24"/>
        <text class="status-text">申请已通过</text>
      </view>

      <view v-if="form.applyStatus === '4'" class="status-tip rejected">
        <u-icon name="close-circle-fill" color="#fa3534" size="24"/>
        <text class="status-text">申请已驳回，请修改后重新提交</text>
      </view>
    </u-form>
    <u-picker :show="showCardTypePicker"
              :columns="[cardTypeOptions]"
              keyName="label"
              @confirm="onCardTypeConfirm"
              @cancel="showCardTypePicker = false"
              closeOnClickOverlay
              @close="showCardTypePicker = false"/>
    <UserSearchPopup
      :show="showInviterPopup"
      title="选择邀请人"
      @close="showInviterPopup = false"
      @select="onInviterSelect"
    />
  </view>
</template>

<script>
import Navbar from '@/components/navbar/Navbar'
import {uploadImage} from '@/utils/upload';
import UserSearchPopup from '@/components/UserSearchPopup/UserSearchPopup';
import {getDicts} from '@/api/system/dict/data'
import {getByUserId, save, update} from '@/api/center/user_apply/user_apply'

import storage from '@/utils/storage'

const {ACCESS_TOKEN} = require('@/store/mutation-types')

// 注意 baseUrl、token 获取方式与 personal.vue 保持一致
const {environment} = require('@/config/environment.js')
const baseUrl = environment.baseURL


export default {
  components: {
    Navbar,
    UserSearchPopup
  },
  data() {
    return {
      form: {
        id: null, // 申请记录ID，用于更新
        userType: '',
        realName: '',
        cardType: '大陆居民身份证',
        idCard: '',
        sex: '',
        phoneNumber: '',
        inviterName: '',
        inviterId: '',
        photoUrl: '',
        photoId: '',
        applyStatus: '1',
      },
      isEdit: false, // 是否为编辑模式
      cardTypeOptions: [],
      cardTypeText: '大陆居民身份证',
      showCardTypePicker: false,
      sexOptions: [
        {label: '男', value: '0'},
        {label: '女', value: '1'}
      ],
      rules: {
        realName: [
          {required: true, message: '请输入真实姓名', trigger: 'blur'},
          {max: 30, message: '姓名不能超过30字符', trigger: 'blur'},
        ],
        cardType: [
          {required: true, message: '请选择证件类型', trigger: 'change'},
        ],
        idCard: [
          {required: true, message: '请输入证件号码', trigger: 'blur'},
          {validator: this.validateIdCard, trigger: 'blur'},
        ],
        sex: [
          {required: true, message: '请选择性别', trigger: 'change'},
        ],
        phoneNumber: [
          {required: true, message: '请输入手机号码', trigger: 'blur'},
          {validator: this.validatePhone, trigger: 'blur'},
        ],
        inviterName: [
          {max: 30, message: '邀请人姓名不能超过30字符', trigger: 'blur'},
        ]
      },
      saveLoading: false,
      saveAndSubmitLoading: false,
      showInviterPopup: false,
      showInviterField: true, // 控制邀请人字段显示
    }
  },
  computed: {
    userTypeLabel() {
      if (this.form.userType === 'aider') return '急救员';
      if (this.form.userType === 'mentor') return '急救导师';
      return this.form.userType;
    },
    // 表单是否禁用
    isFormDisabled() {
      // 当正在加载或状态为审核中(2)、已通过(3)时禁用表单
      // 只有状态为草稿(1)和驳回(4)时才允许编辑
      const disabledStatuses = ['2', '3']; // 2:审核中, 3:已通过
      return this.saveLoading || this.saveAndSubmitLoading
          || disabledStatuses.includes(this.form.applyStatus);
    },
    // 获取申请状态文本
    applyStatusText() {
      const statusMap = {
        '1': '草稿',
        '2': '审核中',
        '3': '已通过',
        '4': '已驳回'
      };
      return statusMap[this.form.applyStatus] || '未知状态';
    }
  },
  onShow() {
    this.restoreFormFromStorage();
    // 检查globalData中是否有realUserInfo
    const app = getApp();
    if (app.globalData && app.globalData.realUserInfo) {
      const realUserInfo = app.globalData.realUserInfo;
      // 当用户类型为导师且存在dealInviterId时，不显示邀请人字段
      if (realUserInfo){
        this.showInviterField = !(this.form.userType === 'mentor' && realUserInfo.dealInviterId);
        this.form.realName = realUserInfo.realName
        this.form.sex = realUserInfo.sex
        this.form.idCard = realUserInfo.idCard
        this.form.phoneNumber = realUserInfo.phonenumber
      }
    } else {
      // 默认显示邀请人字段
      this.showInviterField = true;
    }
  },
  onLoad(options) {
    this.$refs.uForm.setRules(this.rules)
    if (options && options.userType) {
      this.form.userType = options.userType;
    }

    (async () => {
      // 获取证件类型字典
      try {
        const res = await getDicts('sys_card_type');
        this.cardTypeOptions = res.data.map(item => ({
          label: item.dictLabel,
          value: item.dictValue
        }));
        if (this.cardTypeOptions.length > 0) {
          this.form.cardType = this.cardTypeOptions[0].value;
          this.cardTypeText = this.cardTypeOptions[0].label;
        }
      } catch (err) {
        console.error('获取证件类型字典失败:', err);
      }

      // 如果有用户类型，尝试获取现有申请信息
      if (this.form.userType) {
        await this.loadExistingApply();
      }
    })();
  },
  methods: {
    // 获取当前用户ID
    getCurrentUserId() {
      const userInfo = this.$store.state.userInfo;
      return userInfo ? userInfo.userId : null;
    },

    // 加载现有申请信息
    async loadExistingApply() {
      try {
        const userId = this.getCurrentUserId();
        if (!userId) {
          console.warn('用户未登录');
          return;
        }

        const res = await getByUserId(userId, this.form.userType);
        if (res.code === 200 && res.data) {
          // 填充表单数据
          const data = res.data;
          this.form = {
            ...this.form,
            id: data.id,
            realName: data.realName || '',
            cardType: data.cardType || this.form.cardType,
            idCard: data.idCard || '',
            sex: data.sex || '',
            phoneNumber: data.phoneNumber || '',
            inviterName: data.inviterName || '',
            photoUrl: data.photoUrl || '',
            photoId: data.photoId || '',
            applyStatus: data.applyStatus || '1'
          };

          // 更新证件类型显示文本
          const cardTypeOption = this.cardTypeOptions.find(item => item.value === data.cardType);
          if (cardTypeOption) {
            this.cardTypeText = cardTypeOption.label;
          }

          this.isEdit = true;
        }
      } catch (err) {
        console.error('获取申请信息失败:', err);
        // 如果获取失败，可能是还没有申请记录，继续新建流程
      }
    },
    validateIdCard(rule, value, callback) {
      if (!value) {
        callback(new Error('请输入证件号码'))
        return
      }

      // 根据证件类型进行不同的校验
      switch (this.form.cardType) {
        case 'IDCARD':
          this.validateMainlandIdCard(value, callback)
          break
        case 'HKM_PASS':
          this.validateHKMacauPass(value, callback)
          break
        case 'TW_PASS':
          this.validateTaiwanPass(value, callback)
          break
        case 'PASSPORT':
          this.validatePassport(value, callback)
          break
        default:
          callback(new Error('不支持的证件类型'))
          break
      }
    },

    // 大陆身份证校验
    validateMainlandIdCard(value, callback) {
      // 大陆身份证号长度校验
      if (value.length !== 18) {
        callback(new Error('大陆身份证号长度必须为18位'))
        return
      }

      // 大陆身份证号格式校验
      const reg = /^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$/
      if (!reg.test(value)) {
        callback(new Error('大陆身份证号格式不正确'))
        return
      }

      // 校验码验证
      const factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
      const parity = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']
      let sum = 0

      for (let i = 0; i < 17; i++) {
        const ai = parseInt(value[i])
        const wi = factor[i]
        sum += ai * wi
      }

      const last = parity[sum % 11]
      if (last !== value[17].toUpperCase()) {
        callback(new Error('大陆身份证号校验码错误'))
        return
      }

      // 自动识别性别
      const genderCode = parseInt(value.charAt(16))
      this.form.sex = genderCode % 2 === 1 ? '0' : '1'
      callback()
    },

    // 港澳居民来往内地通行证校验
    validateHKMacauPass(value, callback) {
      // 港澳通行证格式：H/M + 8位或10位数字
      const hkMacauReg = /^[HMhm]{1}([0-9]{10}|[0-9]{8})$/
      if (!hkMacauReg.test(value)) {
        callback(new Error('港澳居民来往内地通行证格式不正确'))
        return
      }
      callback()
    },

    // 台湾居民来往大陆通行证校验
    validateTaiwanPass(value, callback) {
      // 台胞证格式：8位或10位数字
      const taiwanReg = /^[0-9]{8,10}$/
      if (!taiwanReg.test(value)) {
        callback(new Error('台湾居民来往大陆通行证格式不正确'))
        return
      }
      callback()
    },

    // 护照校验
    validatePassport(value, callback) {
      // 护照格式：字母+数字组合，长度6-20位
      const passportReg = /^[A-Za-z0-9]{6,20}$/
      if (!passportReg.test(value)) {
        callback(new Error('护照号码格式不正确'))
        return
      }
      callback()
    },

    validatePhone(rule, value, callback) {
      // 简单手机号码校验（中国大陆）
      const phoneReg = /^1[3-9]\d{9}$/;
      if (!phoneReg.test(value)) {
        callback('手机号码格式不正确');
      } else {
        callback();
      }
    },
    chooseImage(type, idField) {
      this.saveFormToStorage();
      uni.chooseImage({
        count: 1,
        success: (res) => {
          const filePath = res.tempFilePaths[0];
          // 校验格式和大小
          uni.getImageInfo({
            src: filePath,
            success: (info) => {
              const isJpgOrPng = info.type === 'jpg' || info.type === 'jpeg' || info.type === 'png';
              if (!isJpgOrPng) {
                this.$u.toast('仅支持JPG/PNG格式图片');
                return;
              }
              // 5MB = 5*1024*1024
              uni.getFileSystemManager().getFileInfo({
                filePath,
                success: (fileInfo) => {
                  if (fileInfo.size > 5 * 1024 * 1024) {
                    this.$u.toast('图片大小不能超过5MB');
                    return;
                  }
                  uni.showLoading({title: '上传中...'});
                  uploadImage(filePath, (url, fileId) => {
                    uni.hideLoading();
                    this.restoreFormFromStorage();
                    this.form[type] = url;
                    this.form[idField] = fileId;
                    this.$refs.uForm.validateField(type);
                    this.clearFormStorage();
                  }, err => {
                    uni.hideLoading();
                    this.$u.toast('图片上传失败');
                    this.restoreFormFromStorage();
                  });
                },
                fail: () => {
                  this.$u.toast('图片读取失败');
                }
              });
            },
            fail: () => {
              this.$u.toast('图片信息获取失败');
            }
          });
        }
      });
    },
    saveFormToStorage() {
      uni.setStorageSync('temp_form_data', JSON.stringify(this.form));
    },

    // 从本地存储恢复表单数据
    restoreFormFromStorage() {
      try {
        const tempData = uni.getStorageSync('temp_form_data');
        if (tempData) {
          this.form = { ...this.form, ...JSON.parse(tempData) };
        }
      } catch (e) {
        console.error('恢复表单数据失败:', e);
      }
    },
    clearFormStorage() {
      uni.removeStorageSync('temp_form_data');
    },
    previewImage(type) {
      if (this.form[type]) {
        uni.previewImage({urls: [this.form[type]]});
      }
    },
    deletePhoto(type) {
      this.form[type] = '';
      if (type === 'photoUrl') this.form.photoId = '';
      this.$refs.uForm.validateField(type);
    },

    onCardTypeConfirm(e) {
      this.cardTypeText = e.value[0].label;
      this.form.cardType = e.value[0].value;
      this.showCardTypePicker = false;
      this.$refs.uForm.validateField('cardType');
    },
    chooseIdCardImage() {
      uni.chooseImage({
        count: 1,
        success: (res) => {
          const filePath = res.tempFilePaths[0];
          this.recognizeIdCard(filePath);
        }
      });
    },
    recognizeIdCard(filePath) {
      if (!filePath) return;
      uni.showLoading({title: '识别中...'});
      uni.uploadFile({
        url: baseUrl + '/wx/user/idCardInfo',
        filePath: filePath,
        header: {
          'Authorization': 'Bearer ' + storage.get(ACCESS_TOKEN)
        },
        name: 'file',
        success: (res) => {
          uni.hideLoading();
          const result = JSON.parse(res.data);
          if (result.code === 200 && result.data.type === 'Front') {
            const data = result.data;
            this.form.realName = data.name;
            this.form.idCard = data.id;
            this.form.sex = data.gender === '男' ? '0' : '1';
            this.$refs.uForm.validateField('idCard');
            this.$refs.uForm.validateField('realName');
            this.$refs.uForm.validateField('sex');
            this.$u.toast('识别成功');
          } else {
            this.$u.toast('识别失败');
          }
        },
        fail: () => {
          uni.hideLoading();
          this.$u.toast('识别失败');
        }
      });
    },
    async handleSave(status) {
      if (this.saveLoading || this.submitLoading || this.saveAndSubmitLoading) return;

      // 根据状态设置不同的loading状态
      if (status === '1') {
        this.saveLoading = true;
      } else {
        this.saveAndSubmitLoading = true;
      }

      try {
        const valid = await this.$refs.uForm.validate();
        if (!valid) {
          this.$u.toast('请填写必填项');
          return;
        }

        const saveData = {
          ...this.form,
          applyStatus: status,
          userId: this.getCurrentUserId()
        };

        let res;
        if (this.isEdit && this.form.id) {
          // 更新申请
          res = await update(saveData);
        } else {
          // 新增申请
          res = await save(saveData);
        }

        if (res.code === 200) {
          // 如果是新增，保存返回的ID
          if (!this.isEdit && res.data) {
            this.form.id = res.data;
            this.isEdit = true;
          }

          const message = status === '1' ? '保存成功' : '保存并提交成功';
          this.$u.toast(message);

          // 如果是保存并提交，成功后返回上一页
          if (status === '2') {
            setTimeout(() => {
              uni.navigateBack();
            }, 1500);
          }
        } else {
          this.$u.toast(res.msg || '操作失败');
        }
      } catch (err) {
        console.error('保存失败:', err);
        this.$u.toast("操作失败，" + err.msg);
      } finally {
        this.saveLoading = false;
        this.saveAndSubmitLoading = false;
      }
    },
    async onSave() {
      await this.handleSave('1');
    },
    async onSaveAndSubmit() {
      await this.handleSave('2');
    },
    onInviterSelect(user) {
      this.form.inviterId = user.userId;
      this.form.inviterName = user.realName;
      this.showInviterPopup = false;
      this.$refs.uForm.validateField('inviterName');
    },
  },
}
</script>

<style scoped>
.user-apply-form {
  padding: 24rpx;
}

/* 重要提示样式 */
.important-tips {
  margin: 24rpx 0 16rpx 0;
  padding: 20rpx 24rpx;
  background: #fff7e6;
  border: 2rpx solid #ffd591;
  border-radius: 8rpx;
  display: flex;
  align-items: flex-start;
}

.tips-text {
  flex: 1;
  margin-left: 16rpx;
  font-size: 26rpx;
  color: #d48806;
  line-height: 1.5;
}

.form-submit-btns {
  margin-top: 48rpx;
}

.photo-upload {
  width: 200rpx;
  height: 200rpx;
  border: 2rpx dashed #dcdfe6;
  border-radius: 8rpx;
  overflow: hidden;
}

.preview-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.preview-image {
  width: 100%;
  height: 100%;
}

.delete-btn {
  position: absolute;
  top: 0;
  right: 0;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom-left-radius: 8rpx;
}

.upload-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
}

.upload-text {
  font-size: 24rpx;
  color: #909399;
  margin-top: 10rpx;
}

.form-tips {
  color: #faad14;
  font-size: 24rpx;
  margin-top: 12rpx;
  margin-left: 4rpx;
}

.user-type-banner {
  margin: 24rpx 0 16rpx 0;
  padding: 16rpx 24rpx;
  background: #f0f5ff;
  border-left: 8rpx solid #2979ff;
  font-size: 28rpx;
  color: #333;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
}

.user-type-text {
  color: #2979ff;
  font-weight: bold;
  margin-left: 8rpx;
}

/* 禁用状态样式 */
.photo-upload.disabled {
  opacity: 0.6;
  pointer-events: none;
}

/* 状态提示样式 */
.status-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32rpx;
  margin-top: 48rpx;
  border-radius: 8rpx;
}

.status-tip.reviewing {
  background: #fff7e6;
  border: 2rpx solid #ffd591;
}

.status-tip.approved {
  background: #f6ffed;
  border: 2rpx solid #b7eb8f;
}

.status-tip.rejected {
  background: #fff2f0;
  border: 2rpx solid #ffccc7;
}

.status-text {
  margin-left: 16rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.status-tip.reviewing .status-text {
  color: #ff9500;
}

.status-tip.approved .status-text {
  color: #19be6b;
}

.status-tip.rejected .status-text {
  color: #fa3534;
}

/* 状态徽章样式 */
.status-badge {
  margin-left: 16rpx;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.status-badge.status-2 {
  background: #fff7e6;
  color: #ff9500;
  border: 1rpx solid #ffd591;
}

.status-badge.status-3 {
  background: #f6ffed;
  color: #19be6b;
  border: 1rpx solid #b7eb8f;
}

.status-badge.status-4 {
  background: #fff2f0;
  color: #fa3534;
  border: 1rpx solid #ffccc7;
}
</style>
