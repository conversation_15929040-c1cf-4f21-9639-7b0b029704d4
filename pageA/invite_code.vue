<template>
  <view class="invite-code-page">
    <Navbar title="我的邀请码" bgColor="#fff" :hideBtn="false" />
    <view class="user-info">
      <image :src="user.avatar || defaultAvatar" class="avatar" />
      <view class="nickname">{{ user.nickName || '未设置昵称' }}</view>
    </view>
    <view class="qrcode-section">
      <image
        :src="qrImgError ? defaultQrImg : inviter_code"
        class="qrcode-img"
        mode="widthFix"
        @error="onQrImgError"
        id="inviteQrImg"
        @click="previewQrCode"
      />
      <view v-if="qrImgError" class="qr-error">二维码加载失败</view>
    </view>
    <view class="download-arrow-center" @click="saveQrCode">
      <u-icon name="arrow-down" color="#007aff" size="56rpx" />
    </view>
    <view class="tip">
      <template v-if="qrImgError">
        邀请码加载失败，可点击下方按钮重新生成邀请码
      </template>
      <template v-else>
        点击下方按钮可下载邀请码图片
      </template>
    </view>
    <template v-if="qrImgError">
      <button class="regen-btn" @click="regenerateInviterCode">重新生成邀请码</button>
    </template>
    <template v-else>
      <button class="share-btn" @click="showShareDialog">
        <u-icon name="share" color="#fff" size="16"></u-icon>
        分享邀请码
      </button>
      <button class="regen-btn" @click="regenerateInviterCode">重新生成邀请码</button>
    </template>

    <!-- 隐藏的Canvas用于生成分享图 -->
    <canvas
      canvas-id="shareCanvas"
      id="shareCanvas"
      style="width: 375px; height: 667px; position: fixed; top: -9999px; left: -9999px;"
    ></canvas>

    <!-- 分享选择弹窗 -->
    <u-popup
      :show="showSharePopup"
      mode="bottom"
      border-radius="20"
      :safe-area-inset-bottom="true"
    >
      <view class="share-popup">
        <view class="share-title">分享邀请码</view>

        <!-- 显示生成的分享图 -->
        <view v-if="shareImagePath" class="share-preview">
          <image
            :src="shareImagePath"
            class="share-image"
            mode="widthFix"
            @click="previewShareImage"
          />
          <text class="share-tip">点击图片可预览</text>
        </view>

        <!-- 分享提示 -->
        <view class="share-notice">
          <u-icon name="info-circle" color="#faad14" size="16"></u-icon>
          <text class="notice-text">小程序无法直接分享图片，请保存到相册后手动分享</text>
        </view>

        <!-- 保存按钮 -->
        <button class="save-share-btn" @click="saveShareImage" :disabled="!shareImagePath">
          <u-icon name="download" color="#fff" size="16"></u-icon>
          保存分享图到相册
        </button>
        <view class="share-cancel" @click="closeShareDialog">取消</view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import { mapState } from 'vuex'
import Navbar from '@/components/navbar/Navbar'
import { getInviterCode,regenerateInviterCode } from '@/api/user'
export default {
  components: { Navbar },
  data() {
    return {
      qrImgError: false,
      inviter_code: '',
      showSharePopup: false,
      shareImagePath: '', // 生成的分享图路径
      isGeneratingShare: false // 是否正在生成分享图
    }
  },
  computed: {
    ...mapState(['userInfo']),
    user() {
      return this.userInfo || {}
    },
    defaultAvatar() {
      return 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0'
    },
    defaultQrImg() {
      return 'https://cdn.uviewui.com/uview/album/1.jpg'
    }
  },
  methods: {
    onQrImgError() {
      this.qrImgError = true
    },
    async fetchInviterCode() {
      try {
        const res = await getInviterCode(this.user.userId)
        if(res.code === 200 && res.data) {
          this.inviter_code = res.data || ''
          this.qrImgError = false
          // 缓存到全局
          getApp().globalData.inviter_code = this.inviter_code
        }
      } catch (e) {
        this.inviter_code = ''
      }
    },

    previewQrCode() {
      const url = this.qrImgError ? this.defaultQrImg : this.inviter_code
      if (!url) return
      uni.previewImage({
        urls: [url],
        current: url
      })
    },
    async regenerateInviterCode() {
      try {
        const res = await regenerateInviterCode(this.user.userId)
        if(res.code === 200 && res.data) {
          this.inviter_code = res.data || ''
          this.qrImgError = false
          // 缓存到全局
          getApp().globalData.inviter_code = this.inviter_code
        }
      } catch (e) {
        this.qrImgError = true
        this.inviter_code = ''
      }
      uni.showToast({ title: '已重新生成邀请码', icon: 'success' })
    },
    // 显示分享弹窗
    async showShareDialog() {
      if (this.isGeneratingShare) return

      try {
        this.isGeneratingShare = true
        uni.showLoading({ title: '生成分享图...' })

        console.log('开始生成分享图...')

        // 生成分享图
        await this.generateShareImage()

        console.log('分享图生成成功:', this.shareImagePath)
        uni.hideLoading()
        this.showSharePopup = true
      } catch (error) {
        uni.hideLoading()
        console.error('生成分享图失败:', error)
        uni.showToast({ title: '生成分享图失败', icon: 'none' })
      } finally {
        this.isGeneratingShare = false
      }
    },

    // 预览分享图
    previewShareImage() {
      if (!this.shareImagePath) return
      uni.previewImage({
        urls: [this.shareImagePath],
        current: this.shareImagePath
      })
    },

    // 关闭分享弹窗
    closeShareDialog() {
      this.showSharePopup = false
    },

    // 生成分享图
    async generateShareImage() {
      return new Promise((resolve, reject) => {
        console.log('创建Canvas上下文...')
        const canvas = uni.createCanvasContext('shareCanvas', this)

        try {
          console.log('开始绘制模板...')
          // 绘制模板背景
          this.drawShareTemplate(canvas)

          console.log('开始绘制二维码...')
          // 绘制二维码
          this.drawQRCodeOnCanvas(canvas)
            .then(() => {
              console.log('二维码绘制完成，开始绘制用户姓名...')
              // 绘制用户姓名
              this.drawUserNameOnCanvas(canvas)

              console.log('开始生成图片...')
              // 生成图片
              canvas.draw(false, () => {
                console.log('Canvas绘制完成，开始转换为图片...')
                setTimeout(() => {
                  uni.canvasToTempFilePath({
                    canvasId: 'shareCanvas',
                    success: (res) => {
                      console.log('图片生成成功:', res.tempFilePath)
                      this.shareImagePath = res.tempFilePath
                      resolve(res.tempFilePath)
                    },
                    fail: (error) => {
                      console.error('canvasToTempFilePath失败:', error)
                      reject(error)
                    }
                  }, this)
                }, 1000) // 增加延迟时间
              })
            })
            .catch((error) => {
              console.error('绘制二维码失败:', error)
              reject(error)
            })
        } catch (error) {
          console.error('生成分享图异常:', error)
          reject(error)
        }
      })
    },

    // 绘制分享图模板背景
    drawShareTemplate(canvas) {
      console.log('绘制背景模板...')

      // TODO: 替换为实际的模板图片路径
      // const templatePath = '/static/images/share-template.png'

      // 临时用纯色背景
      canvas.setFillStyle('#f7f7f7')
      canvas.fillRect(0, 0, 375, 667)

      // 绘制标题区域
      canvas.setFillStyle('#ffffff')
      canvas.fillRect(20, 50, 335, 100)

      // 绘制标题文字
      canvas.setFontSize(24)
      canvas.setFillStyle('#333333')
      canvas.setTextAlign('center')
      canvas.fillText('忻道邀请码', 187.5, 110)

      console.log('背景模板绘制完成')
    },

    // 在Canvas上绘制二维码
    async drawQRCodeOnCanvas(canvas) {
      if (!this.inviter_code) return Promise.resolve()

      return new Promise((resolve, reject) => {
        uni.downloadFile({
          url: this.inviter_code,
          success: (res) => {
            if (res.statusCode === 200) {
              // 在指定位置绘制二维码 (居中位置)
              const qrSize = 200
              const qrX = (375 - qrSize) / 2
              const qrY = 200

              canvas.drawImage(res.tempFilePath, qrX, qrY, qrSize, qrSize)
              resolve()
            } else {
              reject(new Error('二维码下载失败'))
            }
          },
          fail: reject
        })
      })
    },

    // 在Canvas上绘制用户姓名
    drawUserNameOnCanvas(canvas) {
      const userName = this.user.realName || this.user.nickName || '朋友'

      // 绘制邀请文案
      canvas.setFontSize(20)
      canvas.setFillStyle('#333333')
      canvas.setTextAlign('center')
      canvas.fillText(`${userName} 邀请您加入忻道`, 187.5, 450)

      // 绘制副标题
      canvas.setFontSize(16)
      canvas.setFillStyle('#666666')
      canvas.fillText('扫描二维码立即加入', 187.5, 480)
    },

    // 保存分享图到相册
    saveShareImage() {
      if (!this.shareImagePath) {
        uni.showToast({ title: '分享图生成中，请稍候', icon: 'none' })
        return
      }

      uni.showLoading({ title: '保存中...' })

      // #ifdef MP-WEIXIN
      uni.saveImageToPhotosAlbum({
        filePath: this.shareImagePath,
        success: () => {
          uni.hideLoading()
          uni.showToast({ title: '保存成功', icon: 'success' })
          this.closeShareDialog()
        },
        fail: (error) => {
          uni.hideLoading()
          console.error('保存失败:', error)
          if (error.errMsg.includes('auth')) {
            uni.showModal({
              title: '提示',
              content: '需要授权访问相册才能保存图片',
              confirmText: '去设置',
              success: (res) => {
                if (res.confirm) {
                  uni.openSetting()
                }
              }
            })
          } else {
            uni.showToast({ title: '保存失败', icon: 'none' })
          }
        }
      })
      // #endif

      // #ifndef MP-WEIXIN
      uni.showToast({ title: '请在微信小程序中使用保存功能', icon: 'none' })
      // #endif
    },

    // 微信小程序分享功能 - 分享给朋友
    onShareAppMessage(res) {
      const currentUser = this.$store.state.userInfo
      return {
        title: `${currentUser?.realName || currentUser?.nickName || '朋友'}邀请您加入忻道`,
        path: `/page/index?inviterId=${currentUser?.userId}`,
        imageUrl: this.shareImagePath || 'https://drxin-1359893946.cos.ap-shanghai.myqcloud.com/drxin-project/dev/share_logo.png'
      }
    },

    // 微信小程序分享功能 - 分享到朋友圈
    onShareTimeline() {
      const currentUser = this.$store.state.userInfo
      return {
        title: `${currentUser?.realName || currentUser?.nickName || '朋友'}邀请您加入忻道`,
        imageUrl: this.shareImagePath || 'https://drxin-1359893946.cos.ap-shanghai.myqcloud.com/drxin-project/dev/share_logo.png'
      }
    }
  },
  async onLoad() {
    const app = getApp()
    if(app.globalData.inviter_code) {
      this.inviter_code = app.globalData.inviter_code
    } else {
      await this.fetchInviterCode()
    }
  }
}
</script>

<style lang="scss">
.invite-code-page {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 32rpx;
  background: #f7f7f7;
  min-height: 100vh;
}
.user-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 48rpx;
  .avatar {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    margin-bottom: 16rpx;
  }
  .nickname {
    font-size: 32rpx;
    color: #333;
    font-weight: bold;
  }
}
.qrcode-section {
  background: #fff;
  border-radius: 24rpx;
  padding: 48rpx 32rpx 32rpx 32rpx;
  box-shadow: 0 4rpx 24rpx #eee;
  display: flex;
  flex-direction: column;
  align-items: center;
  .qrcode-img {
    width: 300rpx;
    margin-bottom: 24rpx;
  }
  .qr-error {
    color: #e43d33;
    font-size: 28rpx;
    margin-top: 12rpx;
  }
}
.download-arrow-center {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  margin: 24rpx 0 0 0;
}
.tip {
  margin-top: 48rpx;
  color: #999;
  font-size: 26rpx;
}

.share-btn {
  margin-top: 24rpx;
  width: 80%;
  background: #07c160;
  color: #fff;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  padding: 20rpx 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.regen-btn {
  margin-top: 24rpx;
  width: 80%;
  background: #f5a623;
  color: #fff;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  padding: 20rpx 0;
}

/* 分享弹窗样式 */
.share-popup {
  padding: 40rpx 32rpx;
  background: #fff;
}

.share-title {
  text-align: center;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 48rpx;
}

/* 分享图预览样式 */
.share-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 32rpx;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.share-image {
  width: 300rpx;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.share-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 16rpx;
}

/* 分享提示样式 */
.share-notice {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: #fff7e6;
  border: 1rpx solid #ffd591;
  border-radius: 12rpx;
  margin-bottom: 32rpx;
  gap: 12rpx;
}

.notice-text {
  font-size: 26rpx;
  color: #d48806;
  line-height: 1.4;
  flex: 1;
}

/* 保存分享图按钮样式 */
.save-share-btn {
  width: 100%;
  background: #07c160;
  color: #fff;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  padding: 24rpx 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  margin-bottom: 24rpx;
}

.save-share-btn:disabled {
  background: #ccc;
  color: #999;
}

.share-cancel {
  text-align: center;
  font-size: 32rpx;
  color: #666;
  padding: 24rpx 0;
  border-top: 1rpx solid #eee;
}
</style> 