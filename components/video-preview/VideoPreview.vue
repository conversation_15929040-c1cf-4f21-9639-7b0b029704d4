<template>
  <u-popup :show="show" mode="center" @close="handleClose">
    <view class="video-preview-popup">
      <video
        :src="videoUrl"
        :autoplay="autoplay"
        :loop="loop"
        :muted="muted"
        :object-fit="objectFit"
        :controls="controls"
        :poster="poster"
        @error="handleVideoError"
        @play="handlePlay"
        @pause="handlePause"
        @ended="handleEnded"
        @timeupdate="handleTimeUpdate"
        class="video-preview-player"
      />
      <view class="video-close-btn" @click="handleClose">
        <u-icon name="close" size="24" color="#fff" />
      </view>
    </view>
  </u-popup>
</template>

<script>
export default {
  name: 'VideoPreview',
  props: {
    // 是否显示弹窗
    show: {
      type: Boolean,
      default: false
    },
    // 视频URL
    videoUrl: {
      type: String,
      default: ''
    },
    // 视频封面
    poster: {
      type: String,
      default: ''
    },
    // 是否自动播放
    autoplay: {
      type: Boolean,
      default: false
    },
    // 是否循环播放
    loop: {
      type: Boolean,
      default: false
    },
    // 是否静音
    muted: {
      type: Boolean,
      default: false
    },
    // 视频适应方式
    objectFit: {
      type: String,
      default: 'contain',
      validator: (value) => ['contain', 'fill', 'cover'].includes(value)
    },
    // 是否显示控制条
    controls: {
      type: Boolean,
      default: true
    },
    // 弹窗宽度
    width: {
      type: String,
      default: '600rpx'
    },
    // 弹窗高度
    height: {
      type: String,
      default: '400rpx'
    }
  },
  
  methods: {
    // 关闭弹窗
    handleClose() {
      this.$emit('close');
    },
    
    // 视频播放错误
    handleVideoError(e) {
      console.error('视频播放错误:', e);
      this.$emit('error', e);
      uni.showToast({ title: '视频播放失败', icon: 'none' });
    },
    
    // 视频开始播放
    handlePlay(e) {
      this.$emit('play', e);
    },
    
    // 视频暂停
    handlePause(e) {
      this.$emit('pause', e);
    },
    
    // 视频播放结束
    handleEnded(e) {
      this.$emit('ended', e);
    },
    
    // 播放进度更新
    handleTimeUpdate(e) {
      this.$emit('timeupdate', e);
    }
  }
}
</script>

<style lang="scss" scoped>
.video-preview-popup {
  position: relative;
  width: v-bind(width);
  height: v-bind(height);
  background: #000;
  border-radius: 16rpx;
  overflow: hidden;
  
  .video-preview-player {
    width: 100%;
    height: 100%;
  }
  
  .video-close-btn {
    position: absolute;
    top: 16rpx;
    right: 16rpx;
    width: 48rpx;
    height: 48rpx;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    cursor: pointer;
    
    &:hover {
      background: rgba(0, 0, 0, 0.7);
    }
  }
}
</style>
