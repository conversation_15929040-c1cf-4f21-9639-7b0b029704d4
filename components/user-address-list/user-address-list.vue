<template>
  <view class="popup-container">
    <view class="close-container">
      <button class="close-btn" @click="onClose">关闭</button>
    </view>
    <view class="list-container">
      <DataList
          ref="dataList"
          :labels="labels"
          :data="listData"
          :loading="loading"
          :pagination="pagination"
          @refresh="onRefresh"
          @load-more="onLoadMore"
          @item-click="onItemClick"
      />
    </view>
    <button class="fab-add" @click="onAddAddress">新增地址</button>
  </view>
</template>

<script>
import DataList from "@/components/data-list/DataList";
import { getAddressList } from "@/api/center/user_address/user_address"; // 地址列表API

export default {
  name: 'UserAddressList',
  components: { DataList },
  data() {
    return {
      labels: [
        { label: '姓名', prop: 'name' },
        { label: '联系电话', prop: 'phone' },
        { label: '地址', prop: 'address' },
        { label: '是否默认', prop: 'defaultFlag' }
      ],
      listData: [],
      loading: false,
      pagination: {
        page: 1,
        pageSize: 10,
        total: 0,
      },
      rightOptions: [
        { text: '删除', style: {backgroundColor: '#ff4d4f', color: '#fff'}},
      ]
    };
  },
  methods: {
    getList(append = false) {
      this.loading = true;
      getAddressList({
        pageNum: this.pagination.page,
        pageSize: this.pagination.pageSize,
      }).then(res => {
        let rows = res.rows;
        rows.forEach(item => {
          item.defaultFlag = item.defaultFlag == 'Y' ? '是' : '否';
        });
        if (append) {
          this.listData = this.listData.concat(rows);
        } else {
          this.listData = rows;
        }
        this.pagination.total = res.total;
      }).finally(() => {
        this.loading = false;
        // 停止 DataList 组件的刷新状态
        if (this.$refs.dataList) {
          this.$refs.dataList.stopRefresh();
        }
      });
    },
    onItemClick(item) {
      this.$emit('address-selected', item);  // 返回选中的地址
      this.onClose();
    },
    onRefresh() {
      this.pagination.page = 1;
      this.getList(false);
    },
    onLoadMore() {
      if (this.pagination.page < Math.ceil(this.pagination.total / this.pagination.pageSize)) {
        this.pagination.page++;
        this.getList(true);
      }
    },
    onShow() {
      this.getList();
    },
    onAddAddress() {
      uni.navigateTo({ url: '/pageA/user_address/form' });
    },
    onClose() {
      this.$emit('close');  // 关闭弹出框
    }
  },
  mounted() {
    this.getList();
  }
};
</script>

<style lang="scss" scoped>
.popup-container {
  height: 80vh;
  display: flex;
  flex-direction: column;
  position: relative;
  background: white;
}

.close-container {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  z-index: 10;
}



.close-btn {
  background: none;  /* 移除蓝色背景 */
  color: #333;  /* 使用深色字体 */
  border: none;
  font-size: 32rpx;  /* 设置合适的字体大小 */
  padding: 0;
  line-height: 1;
}


.list-container {
  flex: 1;
  padding: 0 16rpx 120rpx 16rpx;
}

.fab-add {
  position: fixed;
  right: 48rpx;
  bottom: 120rpx;
  z-index: 99;
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 16rpx;
  min-width: 180rpx;
  height: 80rpx;
  font-size: 32rpx;
  box-shadow: 0 4rpx 16rpx #bbb;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 32rpx;
}
</style>
