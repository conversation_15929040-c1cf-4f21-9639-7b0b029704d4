<template>
  <u-popup :show="show" mode="center" @close="$emit('close')">
    <view class="user-search-popup">
      <u-icon name="close" size="22" class="popup-close" @click="$emit('close')" />
      <view class="popup-title">{{ title }}</view>
      <view class="popup-input-row">
        <u--input v-model="keyword" placeholder="输入微信昵称/姓名/证件号码搜索" class="popup-input"/>
        <u-button
          type="primary"
          size="mini"
          shape="circle"
          class="popup-search-btn"
          :customStyle="{padding: '0 18rpx', height: '64rpx', borderRadius: '32rpx', width: '80rpx', marginLeft: '12rpx'}"
          @click="searchUser"
          :loading="loading"
          :disabled="loading"
        >查询</u-button>
      </view>

      <!-- 加载状态 -->
      <view v-if="loading" class="popup-loading">
        <u-loading-icon mode="circle" size="24" />
        <text class="loading-text">搜索中...</text>
      </view>

      <!-- 搜索结果 -->
      <view v-else-if="userList && userList.length" class="popup-result-container">
        <!-- 标题行 -->
        <view class="result-header">
          <view class="header-item name-header">姓名</view>
          <view class="header-item phone-header">手机号</view>
          <view class="header-item id-header">身份证号</view>
        </view>

        <!-- 结果列表 -->
        <view class="popup-result-list">
          <view v-for="user in userList" :key="user.userId" @click="chooseUser(user)" class="popup-result-item">
            <view class="result-item-content">
              <view class="item-cell name-cell">{{ user.realName || user.name }}</view>
              <view class="item-cell phone-cell">{{ user.phoneNumber || user.phone }}</view>
              <view class="item-cell id-cell">{{ user.idCard }}</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 无结果提示 -->
      <view v-else-if="isEmpty" class="popup-no-result">
        <u-icon name="search" size="32" color="#c8c9cc" />
        <text>未找到相关用户</text>
      </view>

      <!-- 初始提示 -->
      <view v-else class="popup-no-result">
        <u-icon name="account" size="32" color="#c8c9cc" />
        <text>请输入昵称/姓名/证件号码进行搜索</text>
      </view>
    </view>
  </u-popup>
</template>

<script>
import { selectMemberList } from '@/api/user'

export default {
  name: 'UserSearchPopup',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '搜索用户'
    },
    userType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      keyword: '',
      userList: [],
      loading: false,
      isEmpty: false
    }
  },
  methods: {
    async searchUser() {
      if (!this.keyword.trim()) {
        this.$u.toast('请输入搜索关键词');
        return;
      }

      this.loading = true;
      try {
        const params = {
          keyWord: this.keyword.trim(),
          pageNum: 1,
          pageSize: 20
        };

        // 如果传入了userType参数，添加到查询条件中
        if (this.userType) {
          params.updateUserType = this.userType;
        }

        const res = await selectMemberList(params);

        if (res.code === 200) {
          this.userList = res.rows || res.data || [];
          if (this.userList.length === 0) {
            this.isEmpty = true
          }
        } else {
          this.$u.toast(res.msg || '搜索失败');
          this.userList = [];
        }
      } catch (err) {
        console.error('搜索用户失败:', err);
        this.$u.toast('搜索失败');
        this.userList = [];
      } finally {
        this.loading = false;
      }
    },
    chooseUser(user) {
      this.$emit('select', user);
      this.userList = [];
      this.keyword = '';
    }
  }
}
</script>

<style scoped>
.user-search-popup {
  position: relative;
  width: 320px;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.10);
  padding: 32rpx 24rpx 24rpx 24rpx;
  box-sizing: border-box;
}
.popup-close {
  position: absolute;
  right: 18rpx;
  top: 18rpx;
  color: #bbb;
  z-index: 2;
}
.popup-title {
  font-size: 30rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 24rpx;
}
.popup-input-row {
  display: flex;
  align-items: center;
  margin-bottom: 18rpx;
}
.popup-input {
  flex: 1;
  margin-right: 12rpx;
  margin-bottom: 0;
}
.popup-search-btn {
  flex-shrink: 0;
  width: 80rpx;
  margin-bottom: 0;
}

/* 搜索结果容器 */
.popup-result-container {
  margin-top: 16rpx;
}

/* 标题行样式 */
.result-header {
  display: flex;
  background: #f8f9fa;
  border-radius: 8rpx 8rpx 0 0;
  border: 1rpx solid #e9ecef;
  padding: 16rpx 12rpx;
}

.header-item {
  font-size: 26rpx;
  font-weight: 600;
  color: #495057;
  text-align: center;
}

.name-header {
  flex: 2;
  min-width: 120rpx;
}

.phone-header {
  flex: 2.5;
  min-width: 160rpx;
}

.id-header {
  flex: 3;
  min-width: 200rpx;
}

/* 结果列表 */
.popup-result-list {
  max-height: 300rpx;
  overflow-y: auto;
  border: 1rpx solid #e9ecef;
  border-top: none;
  border-radius: 0 0 8rpx 8rpx;
}

.popup-result-item {
  border-bottom: 1rpx solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.popup-result-item:hover {
  background-color: #f8f9fa;
}

.popup-result-item:last-child {
  border-bottom: none;
}

.popup-result-item:active {
  background-color: #e9ecef;
}

/* 结果项内容 */
.result-item-content {
  display: flex;
  padding: 16rpx 12rpx;
  align-items: center;
}

.item-cell {
  font-size: 26rpx;
  color: #333;
  text-align: center;
  word-break: break-all;
  line-height: 1.4;
}

.name-cell {
  flex: 2;
  min-width: 120rpx;
  font-weight: 500;
  color: #2c3e50;
}

.phone-cell {
  flex: 2.5;
  min-width: 160rpx;
  color: #495057;
}

.id-cell {
  flex: 3;
  min-width: 200rpx;
  color: #6c757d;
  font-size: 24rpx;
}
.popup-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 0;
}

.loading-text {
  margin-top: 16rpx;
  color: #909399;
  font-size: 26rpx;
}



.popup-no-result {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 0;
  color: #c8c9cc;
  font-size: 26rpx;
}

.popup-no-result text {
  margin-top: 16rpx;
}
</style> 