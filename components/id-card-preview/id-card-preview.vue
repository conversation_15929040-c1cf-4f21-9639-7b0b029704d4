<template>
  <view class="id-card-container">
    <canvas 
      :canvas-id="canvasId" 
      class="id-card-canvas"
      :style="canvasStyle"
      @tap="previewImage"
    />
    <!-- 加载提示 -->
    <view v-if="isDrawing" class="loading-overlay">
      <text class="loading-text">生成中...</text>
    </view>
    <!-- 调试信息 -->
    <view v-if="showDebug" class="debug-info">
      <text>Canvas: {{canvasWidth}}x{{canvasHeight}}</text>
      <text>Scale: {{scale}}</text>
      <text>Drawing: {{isDrawing}}</text>
      <text>Background: {{!!templateBg}}</text>
      <text>Photo: {{!!photoPath}}</text>
      <button @tap="previewImage" size="mini" type="primary">测试预览</button>
      <button @tap="saveToAlbum" size="mini" type="default">保存到相册</button>
    </view>
  </view>
</template>

<script>
export default {
  name: 'IdCardPreview',
  props: {
    name: { type: String, default: '' },
    idCard: { type: String, default: '' },
    sex: { type: String, default: '' },
    photoPath: { type: String, default: '' },
    templateBg: { type: String, default: '' },
    canvasId: { type: String, default: 'idcardCanvas' },
    thumbnail: { type: Boolean, default: false }, // 是否为缩略图 
    previewScale: { type: Number, default: 2 }, // 预览缩放比例
    showDebug: { type: Boolean, default: false } // 是否显示调试信息
  },
  data() {
    return {
      // 标准证件尺寸 88mm x 57mm，按比例计算 (约1.54:1)
      standardWidth: 300,
      standardHeight: 150,
      isDrawing: false,
      drawCount: 0,
      lastImagePath: '' // 缓存最后生成的图片路径
    }
  },
  computed: {
    displayWidth() {
      return this.thumbnail ? 200 : this.standardWidth;
    },
    displayHeight() {
      return this.thumbnail ? 128 : this.standardHeight;
    },
    canvasWidth() {
      return this.thumbnail ? 200 : this.standardWidth * this.previewScale;
    },
    canvasHeight() {
      return this.thumbnail ? 128 : this.standardHeight * this.previewScale;
    },
    canvasStyle() {
      return {
        width: this.displayWidth + 'px',
        height: this.displayHeight + 'px'
      }
    },
    scale() {
      return this.thumbnail ? 1 : this.previewScale;
    }
  },
  watch: {
    name() { this.debounceDrawPreview(); },
    idCard() { this.debounceDrawPreview(); },
    sex() { this.debounceDrawPreview(); },
    photoPath() { this.debounceDrawPreview(); },
    templateBg() { this.debounceDrawPreview(); }
  },
  mounted() {
    setTimeout(() => {
      this.drawPreview();
    }, 200);
  },
  methods: {
    debounceDrawPreview() {
      if (this.drawTimer) {
        clearTimeout(this.drawTimer);
      }
      this.drawTimer = setTimeout(() => {
        this.drawPreview();
      }, 200);
    },

    async drawPreview() {
      if (this.isDrawing) {
        return;
      }
      
      this.isDrawing = true;
      this.drawCount++;

      try {
        const ctx = uni.createCanvasContext(this.canvasId, this);
        if (!ctx) {
          this.isDrawing = false;
          return;
        }

        ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight);
        
        // 绘制背景
        await this.drawBackground(ctx);
        
        // 绘制头像
        await this.drawPhoto(ctx);
        
        // 绘制文字
        this.drawText(ctx);
        
        // 执行绘制
        ctx.draw(false, () => {
          this.isDrawing = false;
          // 清除缓存的图片路径，强制重新生成
          this.lastImagePath = '';
        });
        
      } catch (error) {
        this.isDrawing = false;
      }
    },

    async drawBackground(ctx) {
      if (!this.templateBg) {
        ctx.setFillStyle('#ffffff');
        ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);
        return;
      }

      try {
        const bgPath = await this.getLocalImagePath(this.templateBg);
        
        if (bgPath) {
          ctx.setFillStyle('#ffffff');
          ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);
          ctx.drawImage(bgPath, 0, 0, this.canvasWidth, this.canvasHeight);
        } else {
          ctx.setFillStyle('#ffffff');
          ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);
        }
      } catch (error) {
        ctx.setFillStyle('#ffffff');
        ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);
      }
    },

    async drawPhoto(ctx) {
      if (!this.photoPath) {
        return;
      }

      try {
        const photoPath = await this.getLocalImagePath(this.photoPath);
        console.log("照片地址", photoPath);
        if (!photoPath) return;

        const photoConfig = {
          x: 20 * this.scale,
          y: 50 * this.scale,
          width: 60 * this.scale,
          height: 75 * this.scale
        };

        ctx.drawImage(
          photoPath, 
          photoConfig.x, 
          photoConfig.y, 
          photoConfig.width, 
          photoConfig.height
        );
        ctx.setStrokeStyle('#ccc');
        ctx.setLineWidth(1);
        ctx.strokeRect(photoConfig.x, photoConfig.y, photoConfig.width, photoConfig.height);
      } catch (error) {
      }
    },

    drawText(ctx) {
      const textConfig = {
        startX: 130 * this.scale,
        startY: 60 * this.scale,
        fontSize: 12 * this.scale,
        color: '#333333'
      };

      ctx.setFillStyle(textConfig.color);
      ctx.setFontSize(textConfig.fontSize);
      ctx.setTextAlign('left');

      let currentY = textConfig.startY;
      let currentX = textConfig.startX;
      const sexPostX = currentX + 110 * this.scale;
      const idCardPostX = currentX + (5 * this.scale);
      const idCardPostY = currentY + 20 * this.scale;
      if (this.name) {
        const nameText = `${this.name}`;
        ctx.fillText(nameText, textConfig.startX, currentY);
      }

      if (this.sex) {
        const sexText = `${this.sex}`;
        ctx.fillText(sexText, sexPostX, currentY);
        
      }

      if (this.idCard) {
        const idText = `${this.idCard}`;
        ctx.fillText(idText, idCardPostX, idCardPostY);
      }
    },

    async getLocalImagePath(path) {
      if (!path) return '';

      return new Promise((resolve) => {
        // 本地路径直接返回
        if (path.startsWith('wxfile://') || path.startsWith('/') || path.startsWith('file://')) {
          resolve(path);
          return;
        }

        // 网络地址和其他情况都先尝试下载
        if (/^https?:\/\//.test(path)) {
          uni.downloadFile({
            url: path,
            success: (res) => {
              resolve(res.statusCode === 200 ? res.tempFilePath : '');
            },
            fail: () => {
              resolve('');
            }
          });
        } else {
          // 其他格式使用 getImageInfo
          uni.getImageInfo({
            src: path,
            success: (res) => {
              resolve(res.path);
            },
            fail: () => {
              resolve('');
            }
          });
        }
      });
    },

    // 核心方法：预览图片
    async previewImage() {
      // 如果正在绘制，提示用户等待
      if (this.isDrawing) {
        uni.showToast({ 
          title: '正在生成中，请稍候', 
          icon: 'loading',
          duration: 1500
        });
        return;
      }

      try {
        uni.showLoading({ title: '生成预览图...' });
        // 获取canvas图片
        const imagePath = await this.getCanvasImage();
        
        if (imagePath) {
          uni.hideLoading();
          
          // 预览图片
          uni.previewImage({
            urls: [imagePath],
            current: imagePath,
            success: () => {
            },
            fail: (error) => {
              uni.showToast({
                title: '预览失败', 
                icon: 'error' 
              });
            }
          });
        } else {
          uni.hideLoading();
          uni.showToast({ 
            title: '生成图片失败', 
            icon: 'error' 
          });
        }
        
      } catch (error) {
        uni.hideLoading();
        uni.showToast({
          title: '预览失败：' + (error.errMsg || error.message), 
          icon: 'none' 
        });
      }
    },

    // 获取canvas生成的图片
    async getCanvasImage() {
      // 如果有缓存且canvas没有重新绘制，直接返回缓存
      if (this.lastImagePath && !this.isDrawing) {
        return this.lastImagePath;
      }

      return new Promise((resolve, reject) => {
        // 确保绘制完成
        setTimeout(() => {
          uni.canvasToTempFilePath({
            canvasId: this.canvasId,
            width: this.canvasWidth,
            height: this.canvasHeight,
            destWidth: this.canvasWidth,
            destHeight: this.canvasHeight,
            fileType: 'jpg',
            quality: 1,
            success: (res) => {
              console.log('Canvas转图片成功:', res.tempFilePath);
              this.lastImagePath = res.tempFilePath;
              resolve(res.tempFilePath);
            },
            fail: (error) => {
              reject(error);
            }
          }, this);
        }, 100);
      });
    },

    async saveToAlbum() {
      try {
        uni.showLoading({ title: '保存中...' });
        
        const imagePath = await this.getCanvasImage();
        
        if (!imagePath) {
          throw new Error('获取图片失败');
        }

        await new Promise((resolve, reject) => {
          uni.saveImageToPhotosAlbum({
            filePath: imagePath,
            success: resolve,
            fail: reject
          });
        });

        uni.hideLoading();
        uni.showToast({ title: '保存成功', icon: 'success' });
        
      } catch (error) {
        uni.hideLoading();
        uni.showToast({
          title: '保存失败：' + (error.errMsg || error.message), 
          icon: 'none' 
        });
      }
    }
  },

  beforeDestroy() {
    if (this.drawTimer) {
      clearTimeout(this.drawTimer);
    }
  }
}
</script>

<style scoped>
.id-card-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  position: relative;
}

.id-card-canvas {
  border: 1px solid #eee;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  background-color: #fff;
  cursor: pointer;
  transition: all 0.2s ease;
}

.id-card-canvas:active {
  opacity: 0.8;
  transform: scale(0.98);
}

.loading-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 20rpx 40rpx;
  border-radius: 8rpx;
  z-index: 10;
}

.loading-text {
  font-size: 28rpx;
  color: white;
}

.debug-info {
  margin-top: 20rpx;
  padding: 20rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  font-size: 24rpx;
  width: 100%;
}

.debug-info text {
  display: block;
  margin-bottom: 10rpx;
  color: #666;
}

.debug-info button {
  margin: 10rpx 10rpx 0 0;
}
</style>