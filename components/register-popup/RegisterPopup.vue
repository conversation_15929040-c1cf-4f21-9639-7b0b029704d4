<!-- RegisterPopup.vue -->
<template>
  <u-popup
      :show="show"
      mode="bottom"
      @close="handleClose"
      @open="handleOpen"
      :round="20"
      :safeAreaInsetBottom="true"
  >
    <view class="popup-form">
      <view class="form-header">
        <view class="form-tip">欢迎加入</view>
        <view class="form-subtitle">请填写昵称完成注册</view>
      </view>

      <u-form :model="form" ref="formRef" :rules="rules">
        <u-form-item label="昵称" prop="nickName" required>
          <u--input
              placeholder="请输入您的昵称"
              border="none"
              v-model="form.nickName"
              type="nickname"
              clearable
              :maxlength="20"
              @input="handleNickNameInput"
          />
        </u-form-item>

        <view class="char-count">
          {{ form.nickName.length }}/20
        </view>

        <view class="nickname-tips">
          <text class="tips-icon">💡</text>
          <text class="tips-text">建议填写真实姓名</text>
        </view>

        <view class="agreement-checkbox" style="display: flex; align-items: center; justify-content: center; margin-top: 20rpx;">
          <u-radio-group v-model="agreementChecked" placement="row">
            <u-radio name="agree" shape="circle" activeColor="#2979ff" style="margin-right: 8rpx;" />
          </u-radio-group>
          <text>我已阅读并同意</text>
          <text class="link-text" @click="openAgreement">《用户协议》</text>
          <text>和</text>
          <text class="link-text" @click="openPrivacy">《隐私政策》</text>
        </view>

        <view class="form-actions">
          <u-row gutter="20">
            <u-col span="6">
              <u-button
                  type="info"
                  plain
                  @click="handleClose"
                  :disabled="submitLoading"
                  style="width: 100%;"
              >
                取消
              </u-button>
            </u-col>
            <u-col span="6">
              <u-button
                  type="primary"
                  :loading="submitLoading"
                  @click="handleSubmit"
                  :disabled="!form.nickName.trim()"
                  style="width: 100%;"
              >
                注册并登录
              </u-button>
            </u-col>
          </u-row>
        </view>
      </u-form>
    </view>
  </u-popup>
</template>

<script>
export default {
  name: 'RegisterPopup',
  props: {
    show: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      form: {
        nickName: ''
      },
      submitLoading: false,
      agreementChecked: '',
      rules: {
        nickName: [
          {
            required: true,
            message: '请输入昵称',
            trigger: ['blur', 'change']
          },
          {
            min: 1,
            max: 20,
            message: '昵称长度为1-20个字符',
            trigger: ['blur', 'change']
          },
          {
            pattern: /^[\u4e00-\u9fa5a-zA-Z0-9_-]+$/,
            message: '昵称只能包含中文、英文、数字、下划线和横线',
            trigger: ['blur', 'change']
          }
        ]
      }
    }
  },

  methods: {
    handleOpen() {
      this.$emit('open')
    },

    handleClose() {
      // 重置表单
      this.form.nickName = ''
      this.submitLoading = false
      this.agreementChecked = ''
      this.$emit('close')
    },

    handleNickNameInput(value) {
      // 实时过滤特殊字符
      const filteredValue = value.replace(/[^\u4e00-\u9fa5a-zA-Z0-9_-]/g, '')
      if (filteredValue !== value) {
        this.form.nickName = filteredValue
      }
    },

    async handleSubmit() {
      if (this.submitLoading) return
      if (this.agreementChecked!== 'agree') {
        uni.showToast({
          title: '请先阅读并同意用户协议和隐私政策',
          icon: 'none'
        })
        return
      }
      try {
        // 表单验证
        const valid = await this.$refs.formRef.validate()
        if (!valid) return

        this.submitLoading = true

        // 提交注册信息
        this.$emit('submit', {
          nickName: this.form.nickName.trim()
        })

      } catch (error) {
        console.error('表单验证失败:', error)
        uni.showToast({
          title: '请检查输入内容',
          icon: 'none'
        })
      } finally {
        this.submitLoading = false
      }
    },

    // 打开用户协议
    openAgreement() {
      uni.navigateTo({
        url: '/pageA/legal/agreement',
        fail: () => {
          uni.showToast({
            title: '页面暂未开放',
            icon: 'none'
          })
        }
      })
    },

    // 打开隐私政策
    openPrivacy() {
      uni.navigateTo({
        url: '/pageA/legal/privacy',
        fail: (e) => {
          console.error('隐私政策页面跳转失败:', e)
          uni.showToast({
            title: '页面暂未开放',
            icon: 'none'
          })
        }
      })
    }
  },

  watch: {
    show(newVal) {
      if (!newVal) {
        // 弹窗关闭时重置表单
        this.$nextTick(() => {
          this.form.nickName = ''
          this.submitLoading = false
          this.agreementChecked = ''
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.popup-form {
  padding: 40rpx;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  min-height: 400rpx;
}

.form-header {
  text-align: center;
  margin-bottom: 60rpx;

  .form-tip {
    color: #2979ff;
    font-size: 36rpx;
    font-weight: bold;
    margin-bottom: 10rpx;
  }

  .form-subtitle {
    color: #666;
    font-size: 28rpx;
  }
}

.char-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: -20rpx;
  margin-bottom: 10rpx;
}

.nickname-tips {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
  padding: 16rpx 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  border-left: 4rpx solid #2979ff;

  .tips-icon {
    font-size: 28rpx;
    margin-right: 8rpx;
  }

  .tips-text {
    font-size: 24rpx;
    color: #f39c12;
    line-height: 1.4;
  }
}

.form-actions {
  margin-top: 40rpx;
}

.agreement-text {
  font-size: 24rpx;
  color: #999;
  text-align: center;
  margin-top: 30rpx;
  line-height: 1.5;
}

.link-text {
  color: #2979ff !important;
  text-decoration: underline;
}

.agreement-checkbox {
  margin-top: 20rpx;
  margin-bottom: 40rpx;
}

// 深色模式适配
@media (prefers-color-scheme: dark) {
  .popup-form {
    background-color: #1f1f1f;
  }

  .form-subtitle {
    color: #cccccc;
  }

  .char-count {
    color: #888;
  }

  .nickname-tips {
    background-color: #2a2a2a;
    border-left-color: #4a90e2;

    .tips-text {
      color: #ffd700;
    }
  }

  .agreement-text {
    color: #888;
  }
}
</style>