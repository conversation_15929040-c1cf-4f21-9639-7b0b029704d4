<template>
  <view class="calendar">
    <view class="calendar-header">
      <view class="left">本月上课日历</view>
      <view class="right">{{ year }}年{{ month + 1 }}月</view>
    </view>

    <view class="calendar-grid">
      <view class="calendar-cell calendar-week" v-for="(d, i) in weeks" :key="i">{{ d }}</view>
      <view
          class="calendar-cell"
          v-for="(day, index) in calendarDays"
          :key="index"
          :class="{ today: isToday(day), 'has-course': day.date > 0 && courseDays.includes(day.date)}"
      >
        {{ day.date > 0 ? day.date : '' }}
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'Calendar',
  props: {
    courseDays: {
      type: Array,
      default: () => [] // 示例：上课日期
    }
  },
  data() {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth();

    return {
      year,
      month,
      weeks: ['日', '一', '二', '三', '四', '五', '六'],
      calendarDays: this.buildCalendar(year, month)
    }
  },
  methods: {
    buildCalendar(year, month) {
      const days = [];
      const firstDay = new Date(year, month, 1).getDay();
      const daysInMonth = new Date(year, month + 1, 0).getDate();

      // 前面留空格
      for (let i = 0; i < firstDay; i++) {
        days.push({ date: 0 });
      }

      // 实际日期
      for (let i = 1; i <= daysInMonth; i++) {
        days.push({ date: i });
      }

      return days;
    },
    isToday(day) {
      const now = new Date();
      return (
          day.date === now.getDate() &&
          this.month === now.getMonth() &&
          this.year === now.getFullYear()
      );
    }
  }
}
</script>

<style lang="scss" scoped>
.calendar {
  background: #fff;
  padding: 5rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16rpx;
  font-weight: bold;
  margin-bottom: 5rpx;

  .left {
    text-align: left;
  }

  .right {
    text-align: right;
  }
}


.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 2rpx;
}
.calendar-cell {
  text-align: center;
  padding: 14rpx 0;
  border-radius: 8rpx;
  font-size: 14rpx;
  background-color: #f5f5f5;
}
.calendar-week {
  font-weight: bold;
  color: #888;
  background-color: transparent;
}
.today {
  background-color: #ffe0e0;
}
.has-course {
  background-color: #2979ff;
  color: white;
}
</style>
