<!-- UserInfoCard.vue -->
<template>
  <view class="user-info-card">
    <!-- 设置按钮 -->
    <view v-if="!isGuest" class="settings-btn" @click="handleSettingsClick">
      <u-icon name="setting-fill" color="#606266" size="20"></u-icon>
    </view>

    <view class="user-info">
      <view class="user-avatar">
        <u-avatar
            :src="user.avatar || defaultAvatar"
            size="120rpx"
            :bgColor="!user.avatar ? '#2979ff' : ''"
        />
      </view>
      <view class="user-details">
        <view class="user-info--name">{{ user.realName || user.nickName || '游客' }}</view>
        <text class="user-info--account">
          {{ user.phonenumber || '未设置手机号' }}
        </text>
      </view>
    </view>

    <view class="user-role" v-if="!isGuest">
      <view class="profile-rule">
        <view class="role-text">
          {{ user.roles ? user.roles.map(r => r.roleName).join(', ')  : '未设置角色' }}
        </view>
        <view class="contribution-text">
          贡献值：{{ user.contribution || 0 }}
        </view>
      </view>
    </view>
  </view>
</template>

<script>

export default {
  name: 'UserInfoCard',
  props: {
    user: {
      type: Object,
      default: () => ({})
    },
    isGuest: {
      type: Boolean,
      default: true
    },
    defaultAvatar: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      userTypeLabel: '未登录',
      userTypeDicts: [] // 缓存字典数据
    }
  },
  methods: {
    handleSettingsClick() {
      this.$emit('settings-click')
    }
  }
}
</script>

<style lang="scss" scoped>
.user-info-card {
  padding: 0 40rpx 40rpx 40rpx;
  position: relative;
}

.settings-btn {
  position: absolute;
  top: 50rpx;
  right: 40rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  z-index: 10;
  transition: all 0.2s;

  &:active {
    background-color: rgba(255, 255, 255, 0.7);
    transform: scale(0.95);
  }
}

.user-info {
  display: flex;
  align-items: center;
  padding: 40rpx 0;
  color: #303133;

  .user-avatar {
    flex-shrink: 0;
    margin-right: 30rpx;
  }

  .user-details {
    flex: 1;
  }

  &--name {
    font-size: 56rpx;
    font-weight: bold;
    margin-bottom: 10rpx;
    word-break: break-all;
  }

  &--account {
    font-size: 26rpx;
    color: #909399;
    word-break: break-all;
  }
}

.user-role {
  margin-top: 20rpx;
}

.profile-rule {
  height: 140rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 40rpx;
  background-color: #2979ff;
  border-radius: 16rpx;
  box-shadow: 0px 16rpx 26rpx rgba(0, 0, 0, 0.1);
}

.role-text {
  color: white;
  font-size: 32rpx;
  font-weight: 500;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding-right: 20rpx;
}

.contribution-text {
  color: white;
  font-size: 32rpx;
  font-weight: 500;
  text-align: right;
  flex: 1;
  flex-shrink: 0;
}

// 深色模式适配
@media (prefers-color-scheme: dark) {
  .user-info {
    color: #ffffff;
  }

  .user-info--name {
    color: #000000;
  }

  .user-info--account {
    color: #cccccc;
  }
}
</style>