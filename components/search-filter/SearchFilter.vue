<template>
  <view class="search-filter">
    <!-- 搜索区域 -->
    <view class="search-section">
      <view class="search-bar">
        <u-search
          v-model="searchValue"
          :placeholder="searchPlaceholder"
          :show-action="true"
          action-text="搜索"
          @search="handleSearch"
          @custom="handleSearch"
          bg-color="#ffffff"
        />
      </view>
      
      <!-- 操作按钮 -->
      <view class="action-bar" v-if="filterFields.length > 0">
        <u-button 
          size="mini"
          type="primary" 
          plain 
          @click="showFilterPopup = true"
        >
          筛选
        </u-button>
        <u-button size="mini" type="default" plain @click="handleReset">重置</u-button>
      </view>
    </view>
    
    <!-- 筛选弹窗 -->
    <u-popup 
      v-if="filterFields.length > 0"
      :show="showFilterPopup" 
      mode="bottom" 
      @close="showFilterPopup = false"
    >
      <view class="filter-popup">
        <view class="filter-header">
          <text class="filter-title">筛选条件</text>
          <u-icon name="close" @click="showFilterPopup = false" />
        </view>
        
        <view class="filter-content">
          <u-form>
            <!-- 动态渲染筛选字段 -->
            <view v-for="(field, index) in filterFields" :key="index">
              <!-- 输入框类型 -->
              <u-form-item v-if="field.type === 'input'" :label="field.label">
                <u-input
                  v-model="filterParams[field.key]"
                  :placeholder="field.placeholder || ('请输入' + field.label)"
                />
              </u-form-item>

              <!-- 单选框类型 -->
              <u-form-item v-else-if="field.type === 'radio'" :label="field.label">
                <u-radio-group v-model="filterParams[field.key]" placement="row">
                  <u-radio
                    v-for="(option, optionIndex) in field.options"
                    :key="optionIndex"
                    :name="option.value"
                    :label="option.label"
                  />
                </u-radio-group>
              </u-form-item>

              <!-- 选择器类型 -->
              <u-form-item v-else-if="field.type === 'select'" :label="field.label" @click="handleSelectClick(field)">
                <u-input
                  v-model="filterParams[field.key]"
                  :placeholder="field.placeholder || ('请选择' + field.label)"
                  readonly
                />
              </u-form-item>

              <!-- 日期选择器类型 -->
              <u-form-item v-else-if="field.type === 'date'" :label="field.label" @click="handleDateClick(field)">
                <u-input
                  v-model="filterParams[field.key]"
                  :placeholder="field.placeholder || ('请选择' + field.label)"
                  readonly
                />
              </u-form-item>

              <!-- 日期时间选择器类型 -->
              <u-form-item v-else-if="field.type === 'datetime'" :label="field.label" @click="handleDateTimeClick(field)">
                <u-input
                  v-model="filterParams[field.key]"
                  :placeholder="field.placeholder || ('请选择' + field.label)"
                  readonly
                />
              </u-form-item>
            </view>
          </u-form>
        </view>
        
        <view class="filter-footer">
          <u-button type="primary" @click="handleApplyFilter">确定</u-button>
        </view>
      </view>
    </u-popup>
    
    <!-- 日期选择器 -->
    <u-datetime-picker
      :show="showDatePicker"
      :value="datePickerValue"
      :mode="datePickerMode"
      @confirm="handleDateConfirm"
      @cancel="showDatePicker = false"
    />
    
    <!-- 选择器弹窗 -->
    <u-popup :show="showSelectPopup" mode="bottom" @close="showSelectPopup = false">
      <view class="select-popup">
        <view class="select-header">
          <text class="select-title">{{ currentSelectField && currentSelectField.label }}</text>
          <u-icon name="close" @click="showSelectPopup = false" />
        </view>
        <view class="select-content">
          <view
            v-for="(option, optionIndex) in (currentSelectField && currentSelectField.options)"
            :key="optionIndex"
            class="select-option"
            @click="handleSelectOption(option)"
          >
            {{ option.label }}
          </view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
export default {
  name: 'SearchFilter',
  props: {
    // 搜索占位符
    searchPlaceholder: {
      type: String,
      default: '请输入搜索关键词'
    },
    // 搜索字段key
    searchKey: {
      type: String,
      default: 'keyword'
    },
    // 筛选字段配置
    filterFields: {
      type: Array,
      default: () => []
    },
    // 初始搜索参数
    initialParams: {
      type: Object,
      default: () => ({})
    }
  },
  
  data() {
    return {
      searchValue: '',
      filterParams: {},
      showFilterPopup: false,
      showDatePicker: false,
      showSelectPopup: false,
      datePickerValue: new Date().getTime(),
      datePickerMode: 'date',
      currentDateField: null,
      currentSelectField: null
    };
  },
  
  watch: {
    initialParams: {
      handler(newVal) {
        this.initParams(newVal);
      },
      immediate: true,
      deep: true
    }
  },
  
  methods: {
    // 初始化参数
    initParams(params) {
      this.searchValue = params[this.searchKey] || '';
      this.filterParams = { ...params };
      delete this.filterParams[this.searchKey];
    },
    
    // 搜索
    handleSearch() {
      this.emitChange();
    },
    
    // 重置
    handleReset() {
      this.searchValue = '';
      this.filterParams = {};
      this.emitChange();
    },
    
    // 应用筛选
    handleApplyFilter() {
      this.showFilterPopup = false;
      this.emitChange();
    },
    
    // 日期选择点击
    handleDateClick(field) {
      this.currentDateField = field;
      this.datePickerMode = 'date';
      // 如果已有值，使用已有值，否则使用当前时间
      if (this.filterParams[field.key]) {
        this.datePickerValue = new Date(this.filterParams[field.key]).getTime();
      } else {
        this.datePickerValue = new Date().getTime();
      }
      this.showDatePicker = true;
    },

    // 日期时间选择点击
    handleDateTimeClick(field) {
      this.currentDateField = field;
      this.datePickerMode = 'datetime';
      // 如果已有值，使用已有值，否则使用当前时间
      if (this.filterParams[field.key]) {
        this.datePickerValue = new Date(this.filterParams[field.key]).getTime();
      } else {
        this.datePickerValue = new Date().getTime();
      }
      this.showDatePicker = true;
    },
    
    // 日期确认
    handleDateConfirm({ value }) {
      const date = new Date(value);
      let formattedDate;
      
      if (this.datePickerMode === 'date') {
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        formattedDate = `${year}-${month}-${day}`;
      } else {
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        const hour = date.getHours().toString().padStart(2, '0');
        const minute = date.getMinutes().toString().padStart(2, '0');
        const second = date.getSeconds().toString().padStart(2, '0');
        formattedDate = `${year}-${month}-${day} ${hour}:${minute}:${second}`;
      }
      
      this.filterParams[this.currentDateField.key] = formattedDate;
      this.showDatePicker = false;
    },
    
    // 选择器点击
    handleSelectClick(field) {
      this.currentSelectField = field;
      this.showSelectPopup = true;
    },
    
    // 选择器选项点击
    handleSelectOption(option) {
      this.filterParams[this.currentSelectField.key] = option.value;
      this.showSelectPopup = false;
    },
    
    // 发送变化事件
    emitChange() {
      const params = {
        [this.searchKey]: this.searchValue,
        ...this.filterParams
      };
      this.$emit('change', params);
    }
  }
};
</script>

<style lang="scss" scoped>
.search-filter {
  background: #fff;
  padding: 16rpx;
  margin-bottom: 16rpx;
}

.search-section {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.search-bar {
  flex: 1;
}

.action-bar {
  display: flex;
  gap: 16rpx;
  justify-content: flex-end;
}

.filter-popup, .select-popup {
  background: #fff;
  border-radius: 16rpx 16rpx 0 0;
  padding: 32rpx;
  max-height: 80vh;
  overflow-y: auto;
}

.filter-header, .select-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #eee;
}

.filter-title, .select-title {
  font-size: 36rpx;
  font-weight: bold;
}

.filter-content {
  margin-bottom: 32rpx;
}

.filter-footer {
  display: flex;
  justify-content: center;
}

.select-content {
  .select-option {
    padding: 24rpx 0;
    border-bottom: 1rpx solid #f0f0f0;
    font-size: 28rpx;
    
    &:last-child {
      border-bottom: none;
    }
    
    &:active {
      background: #f5f5f5;
    }
  }
}
</style>
