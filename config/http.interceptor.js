const { environment } = require('./environment.js')
import { ACCESS_TOKEN, REFRESH_TOKEN } from '@/store/mutation-types'
import storage from '@/utils/storage'
import store from '@/store'

// 此vm参数为页面的实例，可以通过它引用vuex中的变量
module.exports = (vm) => {
  uni.$u.http.setConfig((config) => {
    /* 设置 baseURL */
    /* #ifdef H5 */
    config.baseURL = process.env.NODE_ENV === 'production' ? environment.baseURL : '/'
    /* #endif */
    /* #ifndef H5 */
    config.baseURL = environment.baseURL
    /* #endif */
    return config
  })

  // 请求拦截
  uni.$u.http.interceptors.request.use((config) => {
    config.data = config.data || {}
    if (config?.custom?.auth) {
      const accessToken = storage.get(ACCESS_TOKEN)
      if (accessToken) {
        config.header.Authorization = 'Bearer ' + accessToken
      }
    }

    if (config?.custom?.loading) {
      uni.showLoading({
        title: '加载中...',
        mask: true
      })
    }
    return config
  }, config => Promise.reject(config))

  // 响应拦截
  uni.$u.http.interceptors.response.use(async (response) => {
    const data = response.data
    const custom = response.config?.custom

    uni.hideLoading()

    if (data.code !== 200) {
      if (custom.toast !== false && data.code !== 401) {
        uni.$u.toast(data.msg || '请求失败')
      }

      // Token 失效处理
      if (data.code === 401) {
       return handleTokenExpired(response)
      }
      if (custom?.catch) {
        return Promise.reject(data)
      } else {
        return new Promise(() => {})
      }
    }
    return data ?? {}
  }, (response) => {
    uni.hideLoading()
    return Promise.reject(response)
  })
}

// 封装刷新 token 方法
const refreshToken = async () => {
  try {
    const token = storage.get(REFRESH_TOKEN)
    if (!token) {
      throw new Error('无 refresh token')
    }

    const result = await uni.$u.http.post('/refresh', null, {
      custom: { auth: false, catch: true },
      header: { 'refresh-token': token }
    })
    if (result?.code === 200 && result?.data?.accessToken) {
      const { accessToken, refreshToken: newRefreshToken } = result.data
      const refreshExpiryTime = 30 * 86400
      storage.set(ACCESS_TOKEN, accessToken, refreshExpiryTime)
      storage.set(REFRESH_TOKEN, newRefreshToken, refreshExpiryTime)
      return accessToken
    } else {
      console.debug('[RefreshToken] 刷新失败，响应:', result)
      throw new Error('刷新 token 失败')
    }
  } catch (error) {
    console.log('[RefreshToken] 异常:', error)
    throw new Error(error.message || '刷新 token 时发生错误')
  }
}

const handleTokenExpired = async (data) => {
  try {
    // 刷新 token
    const newAccessToken = await refreshToken()
    // 重新发起原始请求
    const originalConfig = data.config
    originalConfig.header.Authorization = `Bearer ${newAccessToken}`
    return await uni.$u.http.request(originalConfig)

  } catch (e) {
    // 登录信息过期，重新登录
    uni.$u.toast(e.message || '登录信息已过期，请重新登录')
    storage.remove(ACCESS_TOKEN)
    storage.remove(REFRESH_TOKEN)
    try {
      await store.dispatch('Logout', {}) // 参数看你接口是否需要，可传空对象
    } catch (logoutError) {
      console.warn('退出登录失败：', logoutError)
    }

    // 跳转到登录页
    uni.reLaunch({
      url: '/pages/center/index'
    })

    // Reject the original request
    return Promise.reject(data)
  }
}
