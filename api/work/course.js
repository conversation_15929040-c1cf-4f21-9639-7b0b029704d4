import request from '@/config/request.js';

export const getCourseList = (data) => request.get('/wx/public/course/list', data)

export const getCourseDetail = (courseId) => request.get(`/wx/public/course/getCourseById/${courseId}`, null)

export const enrollCourse = (data) => request.post('/wx/course_enrollment/enroll', data)

export const cancelEnrollment = (data) => request.post('/wx/course_enrollment/cancelEnrollment', data)

export const checkEnrollment = (param) => request.get(`/wx/course/checkEnrollment`, param)

export const getMyCourseList = (param) => request.get('/wx/course_enrollment/enrollCourseList/', param)

export const checkinStatus = (params) => request.get(`/wx/course_attendance/check-status`, params)

export const performCheckin = (data) => request.post('/wx/course_attendance/checkin', data)

