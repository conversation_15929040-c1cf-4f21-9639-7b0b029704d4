import request from '@/config/request.js';

// 获取用户信息
export const getInfo = () => request.get('/getInfo', null)

// 获取用户贡献值
export const getContribution = () => request.get('/getContribution', null, {custom: { loading: false }})

// 更新用户信息
export const updateInfo = (data) => request.post('/wx/user/updateUserProfile', data)

// 更新个人信息
export const updatePersonalInfo = (data) => request.post('/wx/user/updatePersonalInfo', data)

// 获取个人信息
export const getPersonalInfo = (userId) => request.get('/wx/user/getPersonalInfo', { userId })

// 获取邀请码
export const getInviterCode = (userId) => request.get('/wx/user/personalQrCode', { userId })

// 重新生成邀请码
export const regenerateInviterCode = (userId) => request.get('/wx/user/regenerateInviterCode', { userId })

// 获取成员列表（用于用户搜索）
export const selectMemberList = (params) => request.get('/bizz/member_info/selectMemberList', params)