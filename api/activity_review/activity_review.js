import request from '@/config/request.js';

/**
 * 活动评审API接口
 * 基础路径: /wechat/activity_review
 */

// 查询活动评审列表
export const getActivityReviewList = (params) => request.get('/wechat/activity_review/list', params)

// 获取活动评审详情
export const getActivityReviewInfo = (id) => request.get(`/wechat/activity_review/${id}`, null)

// 新增活动评审
export const saveActivityReview = (data) => request.post('/wechat/activity_review/save', data)

// 修改活动评审
export const updateActivityReview = (data) => request.post('/wechat/activity_review/update', data)

// 删除活动评审
export const deleteActivityReview = (ids) => request.post(`/wechat/activity_review/delete/${ids}`, null)
