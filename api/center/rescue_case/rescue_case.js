import request from '@/config/request.js';

// 查询急救案例列表
export const getRescueCaseList = (params) => request.get('/wx/rescue-case/list', params)

// 获取急救案例详细信息
export const getRescueCaseInfo = (id) => request.get(`/wx/rescue-case/getRescueCaseInfo/${id}`, null)

// 保存急救案例
export const saveRescueCase = (data) => request.post('/wx/rescue-case/save', data)

// 修改急救案例
export const updateRescueCase = (data) => request.post('/wx/rescue-case/update', data)

// 删除急救案例
export const deleteRescueCase = (ids) => request.post(`/wx/rescue-case/delete/${ids}`, null)