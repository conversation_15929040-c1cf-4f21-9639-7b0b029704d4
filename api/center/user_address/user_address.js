import request from '@/config/request.js';

export const getAddressList = (params) => request.get("/wx/user_address/list", params);

export const addAddress = (data) => request.post("/wx/user_address/save", data);

export const getAddressInfo = (id) => request.get(`/wx/user_address/info/${id}`, null);

export const updateAddress = (data) => request.post("/wx/user_address/update", data);

export const deleteAddress = (id) => request.get(`/wx/user_address/delete/${id}`, null);

