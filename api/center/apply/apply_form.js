import request from '@/config/request.js';

// 保存证件申请信息
export const saveApplyInfo = (data) => request.post('/wx/cret-apply/save', data)

// 获取信息消息
export const getApplyInfo = (id) => request.get(   `/wx/cret-apply/getApplyInfo/${id}`,null)

// 获取申请信息列表
export const getApplyList = (params) => request.get('/wx/cret-apply/list', params)

// 删除申请信息
export const deleteApplyInfo = (id) => request.post(   `/wx/cret-apply/delete/${id}`,null)

// 更新申请信息
export const updateApplyInfo = (data) => request.post('/wx/cret-apply/update', data)

// 提交申请
export const submitApply = (id) => request.post(   `/wx/cret-apply/submit/${id}`,null)