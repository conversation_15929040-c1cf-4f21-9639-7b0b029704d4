import request from '@/config/request'

// 查询字典数据列表
export const listData = (query) => request.get('/system/dict/data/list', query)

// 查询字典数据详细
export const getData = (dictCode) => request.get('/system/dict/data/' + dictCode)

// 根据字典类型查询字典数据信息
export const getDicts = (dictType) => request.get('/system/dict/data/type/' + dictType)

// 新增字典数据
export const addData = (data) => request.post('/system/dict/data', data)

// 修改字典数据
export const updateData = (data) => request.put('/system/dict/data', data)

// 删除字典数据
export const delData = (dictCode) => request.delete('/system/dict/data/' + dictCode)
