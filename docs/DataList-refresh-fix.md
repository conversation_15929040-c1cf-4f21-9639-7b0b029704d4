# DataList 组件下拉刷新修复说明

## 问题描述
之前 DataList 组件使用 scroll-view 的下拉刷新功能时，刷新完成后页头的刷新标志不会自动关闭，导致用户体验不佳。

## 问题原因
DataList 组件使用的是 `scroll-view` 组件的内置下拉刷新功能（`refresher-enabled`），但是页面中的刷新处理方法却在调用 `uni.stopPullDownRefresh()`，这个方法只对页面级别的下拉刷新有效，对 `scroll-view` 的刷新状态无效。

对于 `scroll-view` 的下拉刷新，需要使用 `refresher-triggered` 属性来控制刷新状态。

## 解决方案

### 1. DataList 组件修改
- 添加了 `refresher-triggered` 属性绑定
- 添加了 `refresherTriggered` 数据属性
- 修改了 `handleRefresh` 方法，在触发刷新时设置状态
- 添加了 `stopRefresh` 方法供父组件调用

### 2. 使用 DataList 组件的页面修改
- 为 DataList 组件添加 `ref` 属性
- 在数据加载完成后调用 `this.$refs.dataList.stopRefresh()` 停止刷新状态
- 移除不必要的 `uni.stopPullDownRefresh()` 调用

## 使用示例

```vue
<template>
  <view>
    <DataList
      ref="dataList"
      :labels="labels"
      :data="listData"
      :loading="loading"
      @refresh="onRefresh"
      @load-more="onLoadMore"
    />
  </view>
</template>

<script>
export default {
  methods: {
    async loadData() {
      try {
        this.loading = true;
        // 加载数据的逻辑
        const res = await api.getData();
        this.listData = res.data;
      } finally {
        this.loading = false;
        // 停止刷新状态
        if (this.$refs.dataList) {
          this.$refs.dataList.stopRefresh();
        }
      }
    },
    
    onRefresh() {
      this.pagination.page = 1;
      this.loadData();
    }
  }
};
</script>
```

## 需要修改的其他页面
以下页面也使用了 DataList 组件，建议按照相同的方式进行修改：

1. `pageA/activity_review/list.vue`
2. `pageA/course/list.vue`
3. `pageA/rescue_case/list.vue`
4. `pageA/user_address/list.vue`
5. `pages/work/index.vue`
6. `components/user-address-list/user-address-list.vue`

## 注意事项
1. 确保在数据加载完成后调用 `stopRefresh()` 方法
2. 不要同时使用页面级下拉刷新和 scroll-view 下拉刷新
3. 如果需要页面级下拉刷新，应该禁用 DataList 的 `refresher-enabled` 属性
