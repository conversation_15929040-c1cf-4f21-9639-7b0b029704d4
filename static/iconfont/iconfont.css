@font-face {
  font-family: "custom-icon"; /* Project id 4975373 */
  src: url('@/static/iconfont/iconfont.woff2?t=1752561831229') format('woff2'),
       url('@/static/iconfont/iconfont.woff?t=1752561831229') format('woff'),
       url('@/static/iconfont/iconfont.ttf?t=1752561831229') format('truetype');
}

.custom-icon {
  font-family: "custom-icon" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.custom-icon- {
    font-family: "custom-icon" !important;
    font-size: 16px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}


.custom-icon-erweima:before {
  content: "\e60d";
}

