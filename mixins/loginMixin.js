import loginManager from "@/utils/loginUtils";

export const loginMixin = {
    data() {
        return {
            showRegisterPopup: false,
            registerData: null
        }
    },

    onLoad() {
        // 监听显示注册弹窗事件
        uni.$on('showRegisterPopup', this.handleShowRegisterPopup)
    },

    onUnload() {
        // 移除事件监听
        uni.$off('showRegisterPopup', this.handleShowRegisterPopup)
    },

    methods: {
        // 检查登录状态
        checkLoginStatus() {
            const userInfo = this.$store.state.userInfo
            return userInfo && userInfo.guest !== true
        },

        // 处理显示注册弹窗
        handleShowRegisterPopup(data) {
            this.registerData = data
            this.showRegisterPopup = true
        },

        // 处理注册提交
        async handleRegisterSubmit(formData) {
            if (!this.registerData) return

            try {
                // 从localStorage获取邀请人ID
                const storedInviterId = uni.getStorageSync('inviterId') || ''
                const finalInviterId = this.registerData.inviterId || storedInviterId

                await this.$store.dispatch('Login', {
                    code: this.registerData.code,
                    inviterId: finalInviterId, // 使用邀请人ID
                    loginType: 'weixin',
                    nickName: formData.nickName
                })

                await this.$store.dispatch('Info')

                // 注册成功后清除localStorage中的inviterId
                if (storedInviterId) {
                    uni.removeStorageSync('inviterId')
                }

                this.showRegisterPopup = false
                this.registerData.onSuccess && this.registerData.onSuccess()
            } catch (error) {
                console.error('注册失败:', error)
                this.registerData.onError && this.registerData.onError(error)
            }
        },

        // 关闭注册弹窗
        closeRegisterPopup() {
            this.showRegisterPopup = false
            // 如果存在取消回调，则调用它以确保loginLoading状态被重置
            if (this.registerData && this.registerData.onCancel) {
                this.registerData.onCancel()
            }
            this.registerData = null
        },

        // 需要登录才能执行的操作
        async requireLogin(callback, options = {}) {
            try {
                if (this.checkLoginStatus()) {
                    // 已登录，直接执行回调
                    if (typeof callback === 'function') {
                        return await callback()
                    }
                    return true
                }

                // 未登录，显示登录弹窗
                const loginResult = await loginManager.showLoginModal(options)

                if (loginResult && typeof callback === 'function') {
                    // 登录成功后执行回调
                    return await callback()
                }

                return loginResult
            } catch (error) {
                console.error('登录检查失败:', error)
                uni.showToast({
                    title: '操作失败',
                    icon: 'none'
                })
                return false
            }
        },

        // 直接执行登录
        async performLogin() {
            return await loginManager.performWxLogin()
        }
    }
}
